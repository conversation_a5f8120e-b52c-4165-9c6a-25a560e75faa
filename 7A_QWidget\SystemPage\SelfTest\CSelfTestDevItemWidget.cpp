#include "CSelfTestDevItemWidget.h"
#include <QStyle>
#include <QDate>
#include <QDebug>
#include <QVariant>
#include <QBoxLayout>
#include <QApplication>
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CMessageBox.h"
#include "DBControl/CFtpDB.h"
#include "DBControl/CDevInfoDB.h"
#include "CReadWriteXlsxThread.h"
#include "include/ccalctlib.h"
#include "CRunTest.h"
#include "CHeartBeat.h"
#include "COperationUnit.h"

CSelfTestDevItemWidget::CSelfTestDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent)
    : QWidget(parent)
    , m_iMachineID(iMachineID)
    , m_sDevParams(sDevParams)
    , m_eStatus(eDeviceDisconnect)
    , m_strTipsText(tr("提示"))
{
    this->setFixedSize(sDevParams.iItemWidth, sDevParams.iItemHeight);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSetDevStatus, this, &CSelfTestDevItemWidget::SlotSetDevStatus);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalFaultLog, this, &CSelfTestDevItemWidget::SlotFaultLog);

    SlotSetDevStatus(iMachineID, m_eStatus);

    m_pRunTimer = new QTimer(this);
    connect(m_pRunTimer, &QTimer::timeout, this, &CSelfTestDevItemWidget::_SlotRunTimeout);

    m_pLastNameLabel->setVisible(false);
    m_pLastDateLabel->setVisible(false);
    m_pNextNameLabel->setVisible(false);
    m_pNextDateLabel->setVisible(false);
}

int CSelfTestDevItemWidget::GetMachineID() const
{
    return m_iMachineID;
}

void CSelfTestDevItemWidget::UpdateDateLabel(QString strLastDate, QString strNextDate)
{
    m_pLastDateLabel->setText(strLastDate);
    m_pNextDateLabel->setText(strNextDate);
}

void CSelfTestDevItemWidget::SlotSetDevStatus(int iMachineID, DeviceStatus eStatus)
{
    if(m_iMachineID != iMachineID)
        return;

    m_eStatus = eStatus;
    bool bBtnEnable = true;
    QString strPropertyText, strTipsText;
    switch (eStatus)
    {
    case eDeviceDisconnect:
        strPropertyText = "disconnect";
        strTipsText = tr("离线中");
        bBtnEnable = false;
        break;
    case eDeviceSelfTest:
        strPropertyText = "self_test";
        strTipsText = tr("自检中");
        break;
    case eDeviceIdle:
        strPropertyText = "idle";
        strTipsText = tr("空闲中");
        break;
    case eDeviceTesting:
        strPropertyText = "testing";
        strTipsText = tr("测试中");
        break;
    case eDeviceTestDone:
        strPropertyText = "test_done";
        strTipsText = tr("测试完成");
        break;
    case eDeviceTestStopped:
        strPropertyText = "test_stopped";
        strTipsText = tr("测试停止");
        break;
    case eDeviceTestStopping:
        strPropertyText = "test_stopping";
        strTipsText = tr("停止中");
        break;
    case eDeviceTestFail:
        strPropertyText = "test_fail";
        strTipsText = tr("测试失败");
        break;
    case eDeviceFault:
        strPropertyText = "fault";
        strTipsText = tr("故障中");
        bBtnEnable = false;
        break;
    case eDeviceProcessing:
        strPropertyText = "processing";
        strTipsText = tr("处理中");
        break;
    case eDeviceReset:
        strPropertyText = "reset";
        strTipsText = tr("复位中");
        break;
    default:
        break;
    }

    m_pTitleLabel->setProperty("status", strPropertyText);
    m_pTitleLabel->style()->polish(m_pTitleLabel);

    m_pIndexLabel->setProperty("status", strPropertyText);
    m_pIndexLabel->style()->polish(m_pIndexLabel);

    m_pTextLabel->setProperty("status", strPropertyText);
    m_pTextLabel->style()->polish(m_pTextLabel);
    m_pTextLabel->setText(strTipsText);

    m_pCaliBtn->setProperty("status", strPropertyText);
    m_pCaliBtn->style()->polish(m_pCaliBtn);
    m_pCaliBtn->setEnabled(bBtnEnable);
}

void CSelfTestDevItemWidget::SlotFaultLog(int iMachineID, QVariantList qLogList)
{
    if(m_iMachineID != iMachineID)
        return;

    if(!m_pRunTimer->isActive())
        return;

    if(qLogList.size() < 3)
        return;

    int iCode = qLogList.at(2).toInt();
    if(iCode >= 800 && iCode < 1000)
    {
        //上位机生成的自检故障码只记录不停止
        m_qFaultLogList.push_back(qLogList);
        qDebug()<<QString("%1#自检产生故障日志:").arg(iMachineID + 1)<<qLogList;
        return;
    }

    //下位机上报的轻微故障不记录
    int iLevel = qLogList.at(3).toInt();
    if(iLevel < 4)
        return;

    m_qFaultLogList.push_back(qLogList);
    qDebug()<<QString("%1#自检产生故障日志:").arg(iMachineID + 1)<<qLogList;

    //下位机上报严重故障,停止自检
    emit SignalStopSelfTest(m_iMachineID, false); //不需要发stop,另一处会发

    _SelfTestResult(-1);
    m_pRunTimer->stop();
    m_pCaliBtn->setText(tr("开始自检"));
    ShowWarning((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#检测模块自检失败").arg(m_iMachineID+1));
}

void CSelfTestDevItemWidget::_SlotSelfTestBtn()
{
    if(m_pRunTimer->isActive())
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("正在自检，请耐心等待"));
        return;
    }

    if(eDeviceIdle != m_eStatus)
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("只能在空闲状态下自检，请稍后重试"));
        return;
    }

    int iBtnType = ShowQuestion(this->parentWidget(), m_strTipsText, tr("确定要对%1#检测模块进行自检吗").arg(m_iMachineID+1));
    if(QMessageBox::Yes != iBtnType)
        return;

    if(eDeviceIdle != m_eStatus)
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("只能在空闲状态下自检，请稍后重试"));
        return;
    }

    bool bCardExist = CHeartBeat::GetInstance()->GetDeviceHearbeatStruct(m_iMachineID)->bCardboxExist;
#ifdef Q_OS_WIN32
    bCardExist = true;
#endif
    if(false == bCardExist)
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("请先将试剂卡放入检测模块"));
        return;
    }

    iBtnType = ShowQuestion(this->parentWidget(), m_strTipsText, tr("请确认插入的试剂卡是全新的"));
    if(QMessageBox::Yes != iBtnType)
        return;

    m_pCaliBtn->setText(tr("正在自检"));
    m_pRunTimer->start(1000 * 60 * 15);
#ifdef Q_OS_WIN32
    m_pRunTimer->start(60000);
#endif

    emit CRunTest::GetInstance()->SignalUpdateItemStatus(m_iMachineID, eDeviceSelfTest);
    CPublicConfig::GetInstance()->SetSelfTestRunning(m_iMachineID, true);

    QString strStartCmd = CCmdBase::GetJsonCmdString(Method_start_selftest);
    qDebug()<<Q_FUNC_INFO<<m_iMachineID<<strStartCmd;
    COperationUnit::GetInstance()->SendJsonText(m_iMachineID, Method_start_selftest, strStartCmd);

    m_qFaultLogList.clear();
    QString strCurrentTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strSN = CPublicConfig::GetInstance()->GetMachineSN();
    strSN = DeleteSpecialCharacters(strSN);
    m_strInfoXlsxPath = QString("%1_%2#_selftestinfo_%3.xlsx").arg(strCurrentTime).arg(m_iMachineID+1).arg(strSN);
    m_strInfoXlsxPath = CPublicConfig::GetInstance()->GetXlsxDir() + m_strInfoXlsxPath;
    m_strUserXlsxPath = QString("%1_%2#_selftest_%3.xlsx").arg(strCurrentTime).arg(m_iMachineID+1).arg(strSN);
    m_strUserXlsxPath = CPublicConfig::GetInstance()->GetXlsxDir() + m_strUserXlsxPath;
    qDebug()<<QString("%1#开始自检").arg(m_iMachineID+1)<<m_strInfoXlsxPath<<m_strUserXlsxPath;
    m_qBeginTime = QDateTime::currentDateTime();
    emit SignalStartSelfTest(m_iMachineID, m_strInfoXlsxPath);
}

void CSelfTestDevItemWidget::_SlotRunTimeout()
{
    emit SignalStopSelfTest(m_iMachineID, true);

    CPublicConfig::GetInstance()->SignalSaveFaultCode(800, m_iMachineID);

    _SelfTestResult(-1);
    m_pRunTimer->stop();
    m_pCaliBtn->setText(tr("开始自检"));
    ShowWarning((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#检测模块自检超时").arg(m_iMachineID+1));
}

void CSelfTestDevItemWidget::ReceiveSelfTestResult(int iResult)
{
    if(!m_pRunTimer->isActive())
        return;

    _SelfTestResult(iResult);
    m_pRunTimer->stop();
    m_pCaliBtn->setText(tr("开始自检"));

    if(0 != iResult)
    {
        ShowWarning((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#检测模块自检失败").arg(m_iMachineID+1));
        return;
    }

    ShowSuccess((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#检测模块自检成功").arg(m_iMachineID+1));
}

void CSelfTestDevItemWidget::_SelfTestResult(int iResult)
{
    CPublicConfig::GetInstance()->SetSelfTestRunning(m_iMachineID, false);
    emit CPublicConfig::GetInstance()->SignalSelfTestEnd(m_iMachineID, iResult);

    CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID).bRunning = false;
    CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID).strTimingData.clear();

    QDate qDate = QDate::currentDate();
    QString strLastDate = qDate.toString("yyyy-MM-dd");
    m_pLastDateLabel->setText(strLastDate);

    QVariantList qStopList;
    if(0 == iResult)
    {
        qStopList << 0;
        QString strNextDate = qDate.addYears(1).toString("yyyy-MM-dd");
        m_pNextDateLabel->setText(strNextDate);
        CDevInfoDB::GetInstance().UpdateSelftestDate(m_iMachineID, strLastDate, strNextDate);
    }
    else
    {
        qStopList << 1;
        QString strNextDate = strLastDate;
        m_pNextDateLabel->setText(strNextDate);
        CDevInfoDB::GetInstance().UpdateSelftestDate(m_iMachineID, strLastDate, strNextDate);
    }

    QString strStopCmd = CCmdBase::GetJsonCmdString(Method_stop_selftest, qStopList);
    qDebug()<<Q_FUNC_INFO<<m_iMachineID<<strStopCmd;
    COperationUnit::GetInstance()->SendJsonText(m_iMachineID, Method_stop_selftest, strStopCmd);

    QStringList strFaultCodeList;
    for(int i=0; i<m_qFaultLogList.size(); i++)
    {
        QVariantList qOneList = m_qFaultLogList.at(i);
        if(qOneList.size() < 3)
            continue;
        strFaultCodeList << qOneList.at(2).toString();
    }

    QString strDetails = strFaultCodeList.join(',');
    QString strDate = QDate::currentDate().toString("yyyy-MM-dd");
    QString strPath = m_strUserXlsxPath + ";" + m_strInfoXlsxPath;
    CDevInfoDB::GetInstance().AddSelftestResult(m_iMachineID, QString::number(iResult), strDetails, strDate, strPath);

    //普通用户看见的自检表
    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = m_strUserXlsxPath;
    pXlsxStruct->strTableName = "selftest";
    pXlsxStruct->strTitleList << tr("检测模块") << tr("结果") << tr("详情") << tr("日期");

    QString strResult = (0 == iResult ? tr("成功") : tr("失败"));
    QVariantList qVarList;
    qVarList << QString("%1#").arg(m_iMachineID+1) << strResult << strDetails << strDate;
    pXlsxStruct->varWriteDataList << qVarList;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);

    _WriteFaultLogXlsx();
    _WriteOtherXlsx();
}

void CSelfTestDevItemWidget::_WriteFaultLogXlsx()
{
    QStringList strTitleList = {"ID", "Time", "Code", "Level", "Device", "Unit", "Description", "Engineering Description", "Solution"};
    for(int i=0; i<m_qFaultLogList.size(); i++)
    {
        QVariantList &qOneList = m_qFaultLogList[i];
        if(qOneList.isEmpty())
            continue;
        qOneList[0] = i + 1;
    }

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = m_strInfoXlsxPath;
    pXlsxStruct->strTableName = "fault_log";
    pXlsxStruct->strTitleList = strTitleList;

    pXlsxStruct->varWriteDataList = m_qFaultLogList;
    pXlsxStruct->bAutoAdjustCol = true;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CSelfTestDevItemWidget::_WriteOtherXlsx()
{
    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = m_strInfoXlsxPath;
    pXlsxStruct->strTableName = "other";
    pXlsxStruct->strTitleList << "-" << "-";
    pXlsxStruct->bAutoAdjustCol = false;

    //操作者
    QVariantList qOperatorList = {"Operator", CPublicConfig::GetInstance()->GetLoginUser()};
    pXlsxStruct->varWriteDataList << qOperatorList;

    //开始时间
    QVariantList qBeginTimeList = {"Begin Time", m_qBeginTime.toString("yyyy-MM-dd hh:mm:ss")};
    pXlsxStruct->varWriteDataList << qBeginTimeList;

    //结束时间
    QDateTime qDateTime = QDateTime::currentDateTime();
    QVariantList qEndTimeList = {"End Time", qDateTime.toString("yyyy-MM-dd hh:mm:ss")};
    pXlsxStruct->varWriteDataList << qEndTimeList;

    //耗时
    QString strUseTime = QString("%1 minute").arg(m_qBeginTime.secsTo(qDateTime) / 60.0);
    QVariantList qUseTimeList = {"Use Time", strUseTime};
    pXlsxStruct->varWriteDataList << qUseTimeList;

    //上位机SN
    QVariantList qSNList = {"Console SN", CPublicConfig::GetInstance()->GetMachineSN()};
    pXlsxStruct->varWriteDataList << qSNList;

    //下位机信息
    SPLCVerStruct sPLCStruct = CPublicConfig::GetInstance()->GetPLCVersionStruct(m_iMachineID);

    //下位机SN
    QVariantList qLowMachineSNList = {"Detection Module SN", sPLCStruct.strSN};
    pXlsxStruct->varWriteDataList << qLowMachineSNList;

    //系统版本
    QString strSystemVer;
    ReadFile("/etc/.deploy_info", strSystemVer);
    QVariantList qSystemVerList = {"System Version", strSystemVer};
    pXlsxStruct->varWriteDataList << qSystemVerList;

    //总版本
    QVariantList qFullVerList = {"Full Version", CPublicConfig::GetInstance()->GetFullVersion()};
    pXlsxStruct->varWriteDataList << qFullVerList;

    //软件版本
    QVariantList q7CAPPVerList = {"App Version", GetAppVersion()};
    pXlsxStruct->varWriteDataList << q7CAPPVerList;

    //算法版本
    CCalCTLib ctLib;
    QVariantList qCalcVerList = {"Calc Version", ctLib.getVersion()};
    pXlsxStruct->varWriteDataList << qCalcVerList;

    //中位机版本
    QVariantList qMedianVerList = {"Median Version", sPLCStruct.strMedianShowVersion};
    pXlsxStruct->varWriteDataList << qMedianVerList;

    //PCR版本
    QVariantList qPCRVerList = {"PCR Version", sPLCStruct.strPCRShowVersion};
    pXlsxStruct->varWriteDataList << qPCRVerList;

    //FL版本
    QVariantList qFLVerList = {"FL Version", sPLCStruct.strFLShowVersion};
    pXlsxStruct->varWriteDataList << qFLVerList;

    //自检参数
    QVariantList qParamsList = {"Params", CPublicConfig::GetInstance()->GetSelfTestParamsJson()};
    pXlsxStruct->varWriteDataList << qParamsList;

    FunWriteXlsxEndCallBack lambdaFunction = [](QString strXlsxName, QString strTableName)
    {
        qDebug()<<strXlsxName<<strTableName<<"write end";
        CFtpDB::GetInstance()->AddFtpUploadFile(strXlsxName);
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

QGroupBox *CSelfTestDevItemWidget::_CreateGroupBox()
{
    int iHeight = m_sDevParams.iTitleHeight;

    m_pIndexLabel = new QLabel(QString("%1").arg(m_iMachineID + 1));
    m_pIndexLabel->setFixedSize(m_sDevParams.iIndexWidth, iHeight);
    m_pIndexLabel->setObjectName("IndexLabel");
    m_pIndexLabel->setAlignment(Qt::AlignCenter);
    m_pIndexLabel->setProperty("status", "idle");

    m_pTextLabel = new QLabel(tr("空闲中"));
    m_pTextLabel->setFixedHeight(iHeight);
    m_pTextLabel->setObjectName("StatusLabel");
    m_pTextLabel->setProperty("status", "idle");

    m_pTitleLabel = new QLabel;
    m_pTitleLabel->setFixedSize(m_sDevParams.iItemWidth, iHeight);
    m_pTitleLabel->setObjectName("TitleLabel");
    m_pTitleLabel->setWindowOpacity(0.14);
    m_pTitleLabel->setProperty("status", "idle");

    m_pLastNameLabel = new QLabel(tr("上次自检："));
    m_pLastNameLabel->setObjectName("CalibrationLabel");

    m_pLastDateLabel = new QLabel("2025-07-01");
    m_pLastDateLabel->setObjectName("CalibrationLabel");

    m_pNextNameLabel = new QLabel(tr("推荐自检："));
    m_pNextNameLabel->setObjectName("CalibrationLabel");

    m_pNextDateLabel = new QLabel("2026-07-01");
    m_pNextDateLabel->setObjectName("CalibrationLabel");

    m_pCaliBtn = new QPushButton(tr("开始自检"));
    m_pCaliBtn->setObjectName("ActBtn");
    m_pCaliBtn->setProperty("status", "idle");
    m_pCaliBtn->setFixedSize(m_sDevParams.iItemWidth, m_sDevParams.iTitleHeight);
    connect(m_pCaliBtn, &QPushButton::clicked, this, &CSelfTestDevItemWidget::_SlotSelfTestBtn);

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addWidget(m_pIndexLabel);
    pTitleLayout->addSpacing(15);
    pTitleLayout->addWidget(m_pTextLabel);
    pTitleLayout->addStretch(1);
    m_pTitleLabel->setLayout(pTitleLayout);

    int iDevNum = 1;
    int iItemNum = 8;
    CPublicConfig::GetInstance()->GetDevItemNum(iDevNum, iItemNum);

    bool bVLayout = false;
    if(eLanguage_English == gk_iLanguage && 1 == iDevNum && iItemNum >= 3)
        bVLayout = true;
    if(eLanguage_Spanish == gk_iLanguage && 1 == iDevNum && iItemNum >= 3)
        bVLayout = true;
    if(eLanguage_German == gk_iLanguage && 1 == iDevNum && iItemNum >= 3)
        bVLayout = true;
    if(eLanguage_Italian == gk_iLanguage && 1 == iDevNum && iItemNum >= 3)
        bVLayout = true;

    QBoxLayout *pLastLayout = nullptr;
    if(bVLayout)
    {
        pLastLayout = new QVBoxLayout;
        pLastLayout->setMargin(0);
        pLastLayout->setSpacing(0);
        pLastLayout->addStretch(1);
        pLastLayout->addWidget(m_pLastNameLabel, 0, Qt::AlignHCenter);
        pLastLayout->addWidget(m_pLastDateLabel, 0, Qt::AlignHCenter);
        pLastLayout->addStretch(1);
    }
    else
    {
        pLastLayout = new QHBoxLayout;
        pLastLayout->setMargin(0);
        pLastLayout->setSpacing(5);
        pLastLayout->addStretch(1);
        pLastLayout->addWidget(m_pLastNameLabel);
        pLastLayout->addWidget(m_pLastDateLabel);
        pLastLayout->addStretch(1);
    }

    QBoxLayout *pNextLayout = nullptr;
    if(bVLayout)
    {
        pNextLayout = new QVBoxLayout;
        pNextLayout->setMargin(0);
        pNextLayout->setSpacing(0);
        pNextLayout->addStretch(1);
        pNextLayout->addWidget(m_pNextNameLabel, 0, Qt::AlignHCenter);
        pNextLayout->addWidget(m_pNextDateLabel, 0, Qt::AlignHCenter);
        pNextLayout->addStretch(1);
    }
    else
    {
        pNextLayout = new QHBoxLayout;
        pNextLayout->setMargin(0);
        pNextLayout->setSpacing(5);
        pNextLayout->addStretch(1);
        pNextLayout->addWidget(m_pNextNameLabel);
        pNextLayout->addWidget(m_pNextDateLabel);
        pNextLayout->addStretch(1);
    }

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTitleLabel);
    pLayout->addStretch(1);
    pLayout->addLayout(pLastLayout);
    pLayout->addSpacing(15);
    pLayout->addLayout(pNextLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pCaliBtn);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(m_sDevParams.iItemWidth, m_sDevParams.iItemHeight);
    pGroupBox->setObjectName("PLCItemGroupBox");
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
