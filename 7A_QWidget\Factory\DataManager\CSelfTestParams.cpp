#include "CSelfTestParams.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QJsonObject>
#include <QJsonDocument>

#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

CSelfTestParams::CSelfTestParams(QWidget *parent) : QWidget(parent)
{
    m_iNameWidth = 150;
    m_iValueWidth = 80;
    m_iHeight = 50;
    m_iGasLeftWidth = 210;
    m_iGasRightWidth = 300;
    if(eLanguage_English == gk_iLanguage)
    {
        m_iNameWidth = 190;
        m_iGasLeftWidth = 265;
        m_iGasRightWidth = 330;
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        m_iNameWidth = 170;
        m_iGasLeftWidth = 270;
        m_iGasRightWidth = 350;
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        m_iNameWidth = 170;
        m_iGasLeftWidth = 270;
        m_iGasRightWidth = 270;
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        m_iNameWidth = 190;
        m_iGasLeftWidth = 240;
        m_iGasRightWidth = 310;
    }

    _InitWidget();
    _InitLayout();

    _ReadSelfTestJson();
    _WriteSelfTestJson();
}

void CSelfTestParams::showEvent(QShowEvent *pEvent)
{
    _ReadSelfTestJson();
    _UpdateLineEditValue();

    QWidget::showEvent(pEvent);
}

void CSelfTestParams::_SlotResetBtn()
{
    int iBtnType = ShowQuestion(this, tr("提示"), tr("确定要重置自检参数吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    m_sSelfTestStruct = SDeviceSelfTestParamsStruct();

    _WriteSelfTestJson();
    _UpdateLineEditValue();

    ShowSuccess(this, tr("提示"), tr("自检参数重置成功"));
}

void CSelfTestParams::_SlotSaveBtn()
{
    int iBtnType = ShowQuestion(this, tr("提示"), tr("确定要保存自检参数吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    //TEC
    m_sSelfTestStruct.sTECStruct.dStartTemp = m_pTECStartTempLineEdit->GetLineEditText().toDouble();
    m_sSelfTestStruct.sTECStruct.dMaxTemp = m_pTECMaxTempLineEdit->GetLineEditText().toDouble();
    m_sSelfTestStruct.sTECStruct.dEndTemp = m_pTECEndTempLineEdit->GetLineEditText().toDouble();

    m_sSelfTestStruct.sTECStruct.dUpRate = m_pTECUpRateLineEdit->GetLineEditText().toDouble();
    m_sSelfTestStruct.sTECStruct.dDownRate = m_pTECDownRateLineEdit->GetLineEditText().toDouble();
    m_sSelfTestStruct.sTECStruct.dSpan = m_pTECSpanLineEdit->GetLineEditText().toDouble();

    //Lysis
    m_sSelfTestStruct.sLysisStruct.dMaxTemp = m_pLysisMaxTempLineEdit->GetLineEditText().toDouble();
    m_sSelfTestStruct.sLysisStruct.dUpRate = m_pLysisUpRateLineEdit->GetLineEditText().toDouble();

    //Pressure
    m_sSelfTestStruct.sPressureStruct.gas1.dAddBeginTime = m_pAddTimeRangeLineEdit->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas1.dAddEndTime = m_pAddTimeRangeLineEdit->GetMaxValue();
    m_sSelfTestStruct.sPressureStruct.gas1.dAddMinValue = m_pAddValueRangeLineEdit->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas1.dAddMaxValue = m_pAddValueRangeLineEdit->GetMaxValue();

    m_sSelfTestStruct.sPressureStruct.gas1.dKeepBeginTime = m_pKeepTimeRangeLineEdit->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas1.dKeepEndTime = m_pKeepTimeRangeLineEdit->GetMaxValue();
    m_sSelfTestStruct.sPressureStruct.gas1.dKeepMinValue = m_pKeepValueRangeLineEdit->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas1.dKeepMaxValue = m_pKeepValueRangeLineEdit->GetMaxValue();

    m_sSelfTestStruct.sPressureStruct.gas1.dSubBeginTime = m_pSubTimeRangeLineEdit->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas1.dSubEndTime = m_pSubTimeRangeLineEdit->GetMaxValue();
    m_sSelfTestStruct.sPressureStruct.gas1.dSubMinValue = m_pSubMinValueLineEidt->GetLineEditText().toDouble();

    m_sSelfTestStruct.sPressureStruct.gas2.dAddBeginTime = m_pAddTimeRangeLineEdit2->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas2.dAddEndTime = m_pAddTimeRangeLineEdit2->GetMaxValue();
    m_sSelfTestStruct.sPressureStruct.gas2.dAddMinValue = m_pAddValueRangeLineEdit2->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas2.dAddMaxValue = m_pAddValueRangeLineEdit2->GetMaxValue();

    m_sSelfTestStruct.sPressureStruct.gas2.dKeepBeginTime = m_pKeepTimeRangeLineEdit2->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas2.dKeepEndTime = m_pKeepTimeRangeLineEdit2->GetMaxValue();
    m_sSelfTestStruct.sPressureStruct.gas2.dKeepMinValue = m_pKeepValueRangeLineEdit2->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas2.dKeepMaxValue = m_pKeepValueRangeLineEdit2->GetMaxValue();

    m_sSelfTestStruct.sPressureStruct.gas2.dSubBeginTime = m_pSubTimeRangeLineEdit2->GetMinValue();
    m_sSelfTestStruct.sPressureStruct.gas2.dSubEndTime = m_pSubTimeRangeLineEdit2->GetMaxValue();
    m_sSelfTestStruct.sPressureStruct.gas2.dSubMinValue = m_pSubMinValueLineEidt2->GetLineEditText().toDouble();

    _WriteSelfTestJson();

    ShowSuccess(this, tr("提示"), tr("自检参数保存成功"));
}

void CSelfTestParams::_ReadSelfTestJson()
{
    QString strPath = CPublicConfig::GetInstance()->GetSelfTestJsonFilePath();
    QString strJson;
    if(!ReadFile(strPath, strJson))
        return;
    qDebug()<<"读取自检参数:"<<strJson;

    QJsonParseError err;
    QJsonDocument doc = QJsonDocument::fromJson(strJson.toLocal8Bit(), &err);
    if(QJsonParseError::NoError != err.error)
    {
        qDebug()<<Q_FUNC_INFO<<"自检参数无法解析,将使用默认参数";
        return;
    }

    QJsonObject rootObj = doc.object();

    if(!rootObj.contains("TEC"))
        return;
    QJsonObject tecObj = rootObj.value("TEC").toObject();
    m_sSelfTestStruct.sTECStruct.dStartTemp = tecObj.value("StartTemp").toDouble(45.0);
    m_sSelfTestStruct.sTECStruct.dMaxTemp = tecObj.value("MaxTemp").toDouble(95.0);
    m_sSelfTestStruct.sTECStruct.dEndTemp = tecObj.value("EndTemp").toDouble(45.0);
    m_sSelfTestStruct.sTECStruct.dUpRate = tecObj.value("UpRate").toDouble(9.0);
    m_sSelfTestStruct.sTECStruct.dDownRate = tecObj.value("DownRate").toDouble(6.0);
    m_sSelfTestStruct.sTECStruct.dSpan = tecObj.value("Span").toDouble(5.0);

    if(!rootObj.contains("Lysis"))
        return;
    QJsonObject lysisObj = rootObj.value("Lysis").toObject();
    m_sSelfTestStruct.sLysisStruct.dMaxTemp = lysisObj.value("MaxTemp").toDouble(60.0);
    m_sSelfTestStruct.sLysisStruct.dUpRate = lysisObj.value("UpRate").toDouble(0.5);

    if(!rootObj.contains("Pressure"))
        return;
    QJsonObject pressureObj = rootObj.value("Pressure").toObject();
    if(pressureObj.contains("gas1"))
    {
        QJsonObject gas1Obj = pressureObj.value("gas1").toObject();
        m_sSelfTestStruct.sPressureStruct.gas1.dAddBeginTime = gas1Obj.value("AddBeginTime").toDouble(0);
        m_sSelfTestStruct.sPressureStruct.gas1.dAddEndTime = gas1Obj.value("AddEndTime").toDouble(18.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dAddMinValue = gas1Obj.value("AddMinValue").toDouble(32.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dAddMaxValue = gas1Obj.value("AddMaxValue").toDouble(40.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dKeepBeginTime = gas1Obj.value("KeepBeginTime").toDouble(30.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dKeepEndTime = gas1Obj.value("KeepEndTime").toDouble(60.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dKeepMinValue = gas1Obj.value("KeepMinValue").toDouble(30.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dKeepMaxValue = gas1Obj.value("KeepMaxValue").toDouble(36.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dSubBeginTime = gas1Obj.value("SubBeginTime").toDouble(60.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dSubEndTime = gas1Obj.value("SubEndTime").toDouble(70.0);
        m_sSelfTestStruct.sPressureStruct.gas1.dSubMinValue = gas1Obj.value("SubMinValue").toDouble(5.0);
    }
    if(pressureObj.contains("gas2"))
    {
        QJsonObject gas2Obj = pressureObj.value("gas2").toObject();
        m_sSelfTestStruct.sPressureStruct.gas2.dAddBeginTime = gas2Obj.value("AddBeginTime").toDouble(70);
        m_sSelfTestStruct.sPressureStruct.gas2.dAddEndTime = gas2Obj.value("AddEndTime").toDouble(90);
        m_sSelfTestStruct.sPressureStruct.gas2.dAddMinValue = gas2Obj.value("AddMinValue").toDouble(32.0);
        m_sSelfTestStruct.sPressureStruct.gas2.dAddMaxValue = gas2Obj.value("AddMaxValue").toDouble(40.0);
        m_sSelfTestStruct.sPressureStruct.gas2.dKeepBeginTime = gas2Obj.value("KeepBeginTime").toDouble(100.0);
        m_sSelfTestStruct.sPressureStruct.gas2.dKeepEndTime = gas2Obj.value("KeepEndTime").toDouble(130.0);
        m_sSelfTestStruct.sPressureStruct.gas2.dKeepMinValue = gas2Obj.value("KeepMinValue").toDouble(30.0);
        m_sSelfTestStruct.sPressureStruct.gas2.dKeepMaxValue = gas2Obj.value("KeepMaxValue").toDouble(36.0);
        m_sSelfTestStruct.sPressureStruct.gas2.dSubBeginTime = gas2Obj.value("SubBeginTime").toDouble(130.0);
        m_sSelfTestStruct.sPressureStruct.gas2.dSubEndTime = gas2Obj.value("SubEndTime").toDouble(140.0);
        m_sSelfTestStruct.sPressureStruct.gas2.dSubMinValue = gas2Obj.value("SubMinValue").toDouble(5.0);
    }
}

void CSelfTestParams::_WriteSelfTestJson()
{
    QJsonObject tecObj;
    tecObj.insert("StartTemp", m_sSelfTestStruct.sTECStruct.dStartTemp);
    tecObj.insert("MaxTemp", m_sSelfTestStruct.sTECStruct.dMaxTemp);
    tecObj.insert("EndTemp", m_sSelfTestStruct.sTECStruct.dEndTemp);
    tecObj.insert("UpRate", m_sSelfTestStruct.sTECStruct.dUpRate);
    tecObj.insert("DownRate", m_sSelfTestStruct.sTECStruct.dDownRate);
    tecObj.insert("Span", m_sSelfTestStruct.sTECStruct.dSpan);

    QJsonObject lysisObj;
    lysisObj.insert("MaxTemp", m_sSelfTestStruct.sLysisStruct.dMaxTemp);
    lysisObj.insert("UpRate", m_sSelfTestStruct.sLysisStruct.dUpRate);

    QJsonObject gas1Obj;
    gas1Obj.insert("AddBeginTime", m_sSelfTestStruct.sPressureStruct.gas1.dAddBeginTime);
    gas1Obj.insert("AddEndTime", m_sSelfTestStruct.sPressureStruct.gas1.dAddEndTime);
    gas1Obj.insert("AddMinValue", m_sSelfTestStruct.sPressureStruct.gas1.dAddMinValue);
    gas1Obj.insert("AddMaxValue", m_sSelfTestStruct.sPressureStruct.gas1.dAddMaxValue);
    gas1Obj.insert("KeepBeginTime", m_sSelfTestStruct.sPressureStruct.gas1.dKeepBeginTime);
    gas1Obj.insert("KeepEndTime", m_sSelfTestStruct.sPressureStruct.gas1.dKeepEndTime);
    gas1Obj.insert("KeepMinValue", m_sSelfTestStruct.sPressureStruct.gas1.dKeepMinValue);
    gas1Obj.insert("KeepMaxValue", m_sSelfTestStruct.sPressureStruct.gas1.dKeepMaxValue);
    gas1Obj.insert("SubBeginTime", m_sSelfTestStruct.sPressureStruct.gas1.dSubBeginTime);
    gas1Obj.insert("SubEndTime", m_sSelfTestStruct.sPressureStruct.gas1.dSubEndTime);
    gas1Obj.insert("SubMinValue", m_sSelfTestStruct.sPressureStruct.gas1.dSubMinValue);

    QJsonObject gas2Obj;
    gas2Obj.insert("AddBeginTime", m_sSelfTestStruct.sPressureStruct.gas2.dAddBeginTime);
    gas2Obj.insert("AddEndTime", m_sSelfTestStruct.sPressureStruct.gas2.dAddEndTime);
    gas2Obj.insert("AddMinValue", m_sSelfTestStruct.sPressureStruct.gas2.dAddMinValue);
    gas2Obj.insert("AddMaxValue", m_sSelfTestStruct.sPressureStruct.gas2.dAddMaxValue);
    gas2Obj.insert("KeepBeginTime", m_sSelfTestStruct.sPressureStruct.gas2.dKeepBeginTime);
    gas2Obj.insert("KeepEndTime", m_sSelfTestStruct.sPressureStruct.gas2.dKeepEndTime);
    gas2Obj.insert("KeepMinValue", m_sSelfTestStruct.sPressureStruct.gas2.dKeepMinValue);
    gas2Obj.insert("KeepMaxValue", m_sSelfTestStruct.sPressureStruct.gas2.dKeepMaxValue);
    gas2Obj.insert("SubBeginTime", m_sSelfTestStruct.sPressureStruct.gas2.dSubBeginTime);
    gas2Obj.insert("SubEndTime", m_sSelfTestStruct.sPressureStruct.gas2.dSubEndTime);
    gas2Obj.insert("SubMinValue", m_sSelfTestStruct.sPressureStruct.gas2.dSubMinValue);

    QJsonObject pressureObj;
    pressureObj.insert("gas1", gas1Obj);
    pressureObj.insert("gas2", gas2Obj);

    QJsonObject rootObj;
    rootObj.insert("TEC", tecObj);
    rootObj.insert("Lysis", lysisObj);
    rootObj.insert("Pressure", pressureObj);

    QJsonDocument doc(rootObj);
    QByteArray byteJson = doc.toJson(QJsonDocument::Compact);
    qDebug()<<"保存自检参数:"<<byteJson;

    CPublicConfig::GetInstance()->SetSelfTestParamsJson(byteJson.data());
    CPublicConfig::GetInstance()->SetSelfTestParamsStruct(m_sSelfTestStruct);

    QString strPath = CPublicConfig::GetInstance()->GetSelfTestJsonFilePath();
    WriteFile(strPath, byteJson);
}

void CSelfTestParams::_UpdateLineEditValue()
{
    m_pTECStartTempLineEdit->SetLineEditText(QString::number(m_sSelfTestStruct.sTECStruct.dStartTemp));
    m_pTECMaxTempLineEdit->SetLineEditText(QString::number(m_sSelfTestStruct.sTECStruct.dMaxTemp));
    m_pTECEndTempLineEdit->SetLineEditText(QString::number(m_sSelfTestStruct.sTECStruct.dEndTemp));

    m_pTECUpRateLineEdit->SetLineEditText(QString::number(m_sSelfTestStruct.sTECStruct.dUpRate));
    m_pTECDownRateLineEdit->SetLineEditText(QString::number(m_sSelfTestStruct.sTECStruct.dDownRate));
    m_pTECSpanLineEdit->SetLineEditText(QString::number(m_sSelfTestStruct.sTECStruct.dSpan));

    m_pLysisMaxTempLineEdit->SetLineEditText(QString::number(m_sSelfTestStruct.sLysisStruct.dMaxTemp));
    m_pLysisUpRateLineEdit->SetLineEditText(QString::number(m_sSelfTestStruct.sLysisStruct.dUpRate));

    const SGasSelfTestParmsStruct &gas1 = m_sSelfTestStruct.sPressureStruct.gas1;
    m_pAddTimeRangeLineEdit->SetRangeValue(gas1.dAddBeginTime, gas1.dAddEndTime);
    m_pAddValueRangeLineEdit->SetRangeValue(gas1.dAddMinValue, gas1.dAddMaxValue);
    m_pKeepTimeRangeLineEdit->SetRangeValue(gas1.dKeepBeginTime, gas1.dKeepEndTime);
    m_pKeepValueRangeLineEdit->SetRangeValue(gas1.dKeepMinValue, gas1.dKeepMaxValue);
    m_pSubTimeRangeLineEdit->SetRangeValue(gas1.dSubBeginTime, gas1.dSubEndTime);
    m_pSubMinValueLineEidt->SetLineEditText(QString::number(gas1.dSubMinValue));

    const SGasSelfTestParmsStruct &gas2 = m_sSelfTestStruct.sPressureStruct.gas2;
    m_pAddTimeRangeLineEdit2->SetRangeValue(gas2.dAddBeginTime, gas2.dAddEndTime);
    m_pAddValueRangeLineEdit2->SetRangeValue(gas2.dAddMinValue, gas2.dAddMaxValue);
    m_pKeepTimeRangeLineEdit2->SetRangeValue(gas2.dKeepBeginTime, gas2.dKeepEndTime);
    m_pKeepValueRangeLineEdit2->SetRangeValue(gas2.dKeepMinValue, gas2.dKeepMaxValue);
    m_pSubTimeRangeLineEdit2->SetRangeValue(gas2.dSubBeginTime, gas2.dSubEndTime);
    m_pSubMinValueLineEidt2->SetLineEditText(QString::number(gas2.dSubMinValue));
}

void CSelfTestParams::_InitWidget()
{
    m_pResetBtn = new QPushButton(tr("重置"));
    m_pResetBtn->setFixedSize(120, 50);
    connect(m_pResetBtn, &QPushButton::clicked, this, &CSelfTestParams::_SlotResetBtn);

    m_pSaveBtn = new QPushButton(tr("保存"));
    m_pSaveBtn->setFixedSize(120, 50);
    connect(m_pSaveBtn, &QPushButton::clicked, this, &CSelfTestParams::_SlotSaveBtn);
}

void CSelfTestParams::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(0);
    pTopLayout->addWidget(_CreateTECGroupBox());
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(_CreateLysisGroupBox());

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(50);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pResetBtn);
    pBtnLayout->addWidget(m_pSaveBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(20);
    pLayout->addLayout(pTopLayout);
    pLayout->addWidget(_CreatePressureGroupBox());
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);
    pLayout->addSpacing(24);
    this->setLayout(pLayout);
}

QGroupBox *CSelfTestParams::_CreateTECGroupBox()
{
    m_pTECStartTempLineEdit = new CHLabelLineEdit(tr("起始温度") + "(℃):", "", 5);
    m_pTECStartTempLineEdit->ResetLabelSize(m_iNameWidth, m_iHeight);
    m_pTECStartTempLineEdit->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pTECStartTempLineEdit->SetLineEidtAlignment(Qt::AlignCenter);

    m_pTECMaxTempLineEdit = new CHLabelLineEdit(tr("最大温度") + "(℃):", "", 5);
    m_pTECMaxTempLineEdit->ResetLabelSize(m_iNameWidth, m_iHeight);
    m_pTECMaxTempLineEdit->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pTECMaxTempLineEdit->SetLineEidtAlignment(Qt::AlignCenter);

    m_pTECEndTempLineEdit = new CHLabelLineEdit(tr("结束温度") + "(℃):", "", 5);
    m_pTECEndTempLineEdit->ResetLabelSize(m_iNameWidth, m_iHeight);
    m_pTECEndTempLineEdit->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pTECEndTempLineEdit->SetLineEidtAlignment(Qt::AlignCenter);

    m_pTECUpRateLineEdit = new CHLabelLineEdit(tr("升温速率") + "(℃/S):", "", 5);
    m_pTECUpRateLineEdit->ResetLabelSize(m_iNameWidth, m_iHeight);
    m_pTECUpRateLineEdit->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pTECUpRateLineEdit->SetLineEidtAlignment(Qt::AlignCenter);

    m_pTECDownRateLineEdit = new CHLabelLineEdit(tr("降温速率") + "(℃/S):", "", 5);
    m_pTECDownRateLineEdit->ResetLabelSize(m_iNameWidth, m_iHeight);
    m_pTECDownRateLineEdit->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pTECDownRateLineEdit->SetLineEidtAlignment(Qt::AlignCenter);

    m_pTECSpanLineEdit = new CHLabelLineEdit(tr("温度误差") + "(℃):", "", 5);
    m_pTECSpanLineEdit->ResetLabelSize(m_iNameWidth, m_iHeight);
    m_pTECSpanLineEdit->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pTECSpanLineEdit->SetLineEidtAlignment(Qt::AlignCenter);

    QHBoxLayout *pHLayout1 = new QHBoxLayout;
    pHLayout1->setMargin(0);
    pHLayout1->setSpacing(30);
    pHLayout1->addWidget(m_pTECStartTempLineEdit);
    pHLayout1->addWidget(m_pTECMaxTempLineEdit);
    pHLayout1->addWidget(m_pTECEndTempLineEdit);
    pHLayout1->addStretch(1);

    QHBoxLayout *pHLayout2 = new QHBoxLayout;
    pHLayout2->setMargin(0);
    pHLayout2->setSpacing(30);
    pHLayout2->addWidget(m_pTECSpanLineEdit);
    pHLayout2->addWidget(m_pTECUpRateLineEdit);
    pHLayout2->addWidget(m_pTECDownRateLineEdit);
    pHLayout2->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(20, 25, 5, 0);
    pLayout->setSpacing(20);
    pLayout->addLayout(pHLayout1);
    pLayout->addLayout(pHLayout2);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox(tr("温控"));
    pGroupBox->setFixedSize(890, 160);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}

QGroupBox *CSelfTestParams::_CreateLysisGroupBox()
{
    m_pLysisMaxTempLineEdit = new CHLabelLineEdit(tr("最大温度") + "(℃):", "", 5);
   // m_pLysisMaxTempLineEdit->ResetLabelSize(m_iNameWidth, m_iHeight);
    m_pLysisMaxTempLineEdit->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pLysisMaxTempLineEdit->SetLineEidtAlignment(Qt::AlignCenter);

    m_pLysisUpRateLineEdit = new CHLabelLineEdit(tr("升温速率") + "(℃/S):", "", 5);
   // m_pLysisUpRateLineEdit->ResetLabelSize(m_iNameWidth, m_iHeight);
    m_pLysisUpRateLineEdit->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pLysisUpRateLineEdit->SetLineEidtAlignment(Qt::AlignCenter);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setContentsMargins(20, 15, 5, 0);
    pLayout->setSpacing(30);
    pLayout->addWidget(m_pLysisMaxTempLineEdit);
    pLayout->addWidget(m_pLysisUpRateLineEdit);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox(tr("热裂解"));
    pGroupBox->setFixedSize(580, 160);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}

QGroupBox *CSelfTestParams::_CreatePressureGroupBox()
{
    //气路1
    m_pAddTimeRangeLineEdit = new CRangeLineEdit(tr("气路1注气时间范围") + "(S):");
    m_pAddTimeRangeLineEdit->ResetLabelSize(m_iGasLeftWidth, m_iHeight);
    m_pAddTimeRangeLineEdit->SetRangeValue(0, 18);

    m_pAddValueRangeLineEdit = new CRangeLineEdit(tr("气路1注气压力最大值范围") + "(Kpa):");
    m_pAddValueRangeLineEdit->ResetLabelSize(m_iGasRightWidth, m_iHeight);
    m_pAddValueRangeLineEdit->SetRangeValue(32, 40);

    m_pKeepTimeRangeLineEdit = new CRangeLineEdit(tr("气路1平衡时间范围") + "(S):");
    m_pKeepTimeRangeLineEdit->ResetLabelSize(m_iGasLeftWidth, m_iHeight);
    m_pKeepTimeRangeLineEdit->SetRangeValue(30, 60);

    m_pKeepValueRangeLineEdit = new CRangeLineEdit(tr("气路1平衡压力值范围") + "(Kpa):");
    m_pKeepValueRangeLineEdit->ResetLabelSize(m_iGasRightWidth, m_iHeight);
    m_pKeepValueRangeLineEdit->SetRangeValue(30, 36);

    m_pSubTimeRangeLineEdit = new CRangeLineEdit(tr("气路1泄压时间范围") + "(S):");
    m_pSubTimeRangeLineEdit->ResetLabelSize(m_iGasLeftWidth, m_iHeight);
    m_pSubTimeRangeLineEdit->SetRangeValue(60, 70);

    m_pSubMinValueLineEidt = new CHLabelLineEdit(tr("气路1泄压气压最小值范围") + "(Kpa):", "5", 10);
    m_pSubMinValueLineEidt->ResetLabelSize(m_iGasRightWidth, m_iHeight);
    m_pSubMinValueLineEidt->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pSubMinValueLineEidt->SetLineEidtAlignment(Qt::AlignCenter);

    //气路2
    m_pAddTimeRangeLineEdit2 = new CRangeLineEdit(tr("气路2注气时间范围") + "(S):");
    m_pAddTimeRangeLineEdit2->ResetLabelSize(m_iGasLeftWidth, m_iHeight);
    m_pAddTimeRangeLineEdit2->SetRangeValue(70, 90);

    m_pAddValueRangeLineEdit2 = new CRangeLineEdit(tr("气路2注气压力最大值范围") + "(Kpa):");
    m_pAddValueRangeLineEdit2->ResetLabelSize(m_iGasRightWidth, m_iHeight);
    m_pAddValueRangeLineEdit2->SetRangeValue(32, 40);

    m_pKeepTimeRangeLineEdit2 = new CRangeLineEdit(tr("气路2平衡时间范围") + "(S):");
    m_pKeepTimeRangeLineEdit2->ResetLabelSize(m_iGasLeftWidth, m_iHeight);
    m_pKeepTimeRangeLineEdit2->SetRangeValue(100, 130);

    m_pKeepValueRangeLineEdit2 = new CRangeLineEdit(tr("气路2平衡压力值范围") + "(Kpa):");
    m_pKeepValueRangeLineEdit2->ResetLabelSize(m_iGasRightWidth, m_iHeight);
    m_pKeepValueRangeLineEdit2->SetRangeValue(30, 36);

    m_pSubTimeRangeLineEdit2 = new CRangeLineEdit(tr("气路2泄压时间范围") + "(S):");
    m_pSubTimeRangeLineEdit2->ResetLabelSize(m_iGasLeftWidth, m_iHeight);
    m_pSubTimeRangeLineEdit2->SetRangeValue(130, 140);

    m_pSubMinValueLineEidt2 = new CHLabelLineEdit(tr("气路2泄压气压最小值范围") + "(Kpa):", "5", 10);
    m_pSubMinValueLineEidt2->ResetLabelSize(m_iGasRightWidth, m_iHeight);
    m_pSubMinValueLineEidt2->ResetLineEditSize(m_iValueWidth, m_iHeight);
    m_pSubMinValueLineEidt2->SetLineEidtAlignment(Qt::AlignCenter);

    QHBoxLayout *pHLayout1 = new QHBoxLayout;
    pHLayout1->setMargin(0);
    pHLayout1->setSpacing(30);
    pHLayout1->addWidget(m_pAddTimeRangeLineEdit);
    pHLayout1->addWidget(m_pAddValueRangeLineEdit);
    pHLayout1->addStretch(1);

    QHBoxLayout *pHLayout2 = new QHBoxLayout;
    pHLayout2->setMargin(0);
    pHLayout2->setSpacing(30);
    pHLayout2->addWidget(m_pKeepTimeRangeLineEdit);
    pHLayout2->addWidget(m_pKeepValueRangeLineEdit);
    pHLayout2->addStretch(1);

    QHBoxLayout *pHLayout3 = new QHBoxLayout;
    pHLayout3->setMargin(0);
    pHLayout3->setSpacing(30);
    pHLayout3->addWidget(m_pSubTimeRangeLineEdit);
    pHLayout3->addWidget(m_pSubMinValueLineEidt);
    pHLayout3->addStretch(1);

    QHBoxLayout *pHLayout4 = new QHBoxLayout;
    pHLayout4->setMargin(0);
    pHLayout4->setSpacing(30);
    pHLayout4->addWidget(m_pAddTimeRangeLineEdit2);
    pHLayout4->addWidget(m_pAddValueRangeLineEdit2);
    pHLayout4->addStretch(1);

    QHBoxLayout *pHLayout5 = new QHBoxLayout;
    pHLayout5->setMargin(0);
    pHLayout5->setSpacing(30);
    pHLayout5->addWidget(m_pKeepTimeRangeLineEdit2);
    pHLayout5->addWidget(m_pKeepValueRangeLineEdit2);
    pHLayout5->addStretch(1);

    QHBoxLayout *pHLayout6 = new QHBoxLayout;
    pHLayout6->setMargin(0);
    pHLayout6->setSpacing(30);
    pHLayout6->addWidget(m_pSubTimeRangeLineEdit2);
    pHLayout6->addWidget(m_pSubMinValueLineEidt2);
    pHLayout6->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(20, 25, 20, 0);
    pLayout->setSpacing(20);
    pLayout->addLayout(pHLayout1);
    pLayout->addLayout(pHLayout2);
    pLayout->addLayout(pHLayout3);
    pLayout->addSpacing(10);
    pLayout->addLayout(pHLayout4);
    pLayout->addLayout(pHLayout5);
    pLayout->addLayout(pHLayout6);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox(tr("气压"));
    pGroupBox->setFixedSize(1494, 470);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
