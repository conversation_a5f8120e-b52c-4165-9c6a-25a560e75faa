#include "CMeltingCurve.h"
#include <QTime>
#include <QStyleFactory>
#include "CHistoryDB.h"
#include "sparamfun.h"
#include "CMessageBox.h"
#include "CRunTest.h"

CMeltingCurve::CMeltingCurve(QWidget *parent) : QWidget(parent)
{
    QTime start = QTime::currentTime();

    m_strClassName = "CMeltingCurve";

    Register2Map(Method_fl_data);
    Register2Map(Method_pcr_signal);
    Register2Map(Method_start);

    _InitWidget();
    _InitLayout();
    connect(CRunTest::GetInstance(),&CRunTest::SignalUpdateItemCalcResult,this,&CMeltingCurve::_ShowLast100MeltingID);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTimingTestStart, this, &CMeltingCurve::SlotTestStart);
    _ShowLast100MeltingID();
    qDebug()<<"CMeltingCurve 时间:"<<start.msecsTo(QTime::currentTime());
}

CMeltingCurve::~CMeltingCurve()
{
    UnRegister2Map(Method_fl_data);
    UnRegister2Map(Method_pcr_signal);
    UnRegister2Map(Method_start);
}

void CMeltingCurve::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_fl_data == iMethodID)
    {
        m_pMeltingWidgetList.at(iMachineID)->ParseFLCmd(qVarData);
    }
    else if(Method_pcr_signal == iMethodID)
    {
        m_pMeltingWidgetList.at(iMachineID)->ParsePCRSignalCmd(qVarData);
    }
    else if(Method_start == iMethodID)
    {
        if(CPublicConfig::GetInstance()->GetSelfTestRunning(iMachineID))
        {
            qDebug()<<Q_FUNC_INFO<<QString("%1#自检测试不计算熔解").arg(iMachineID + 1);
        }
        else
        {
            m_pMeltingWidgetList.at(iMachineID)->ParseStartCmd(iResult);
            _ShowLast100MeltingID();
        }
    }
}

void CMeltingCurve::SlotTestStart(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    qDebug()<<QString("时序开始,清空%1#熔解数据").arg(iMachineID + 1);
    m_pMeltingWidgetList.at(iMachineID)->ClearData();
    m_pMeltingWidgetList.at(iMachineID)->InitTestInfo();
}

void CMeltingCurve::_SlotMachineChanged(int iMachineID)
{
    m_pStackedWidget->setCurrentIndex(iMachineID);
}

void CMeltingCurve::_SlotCardIDComboBoxChanged(const QString &strCardID)
{
    m_pCardIDLineEdit->SetLineEditText(strCardID);
}

void CMeltingCurve::_SlotCardIDLineEditTextChanged(const QString &strCardID)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_pMeltingWidgetList.at(iMachineID)->SetCardID(strCardID);
}

void CMeltingCurve::_SlotShowCurrentTestCardID(int iMachineID)
{
    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);
    m_pCardIDLineEdit->SetLineEditText(sRunInfo.sCardInfo.strCardID);
}

void CMeltingCurve::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(100, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    m_pCardIDLineEdit = new CLabelLineEdit(tr("卡盒ID:"));
    m_pCardIDLineEdit->SetLineEditFixedSize(400, 50);
    connect(m_pCardIDLineEdit, &CLabelLineEdit::SignalTextChanged, this, &CMeltingCurve::_SlotCardIDLineEditTextChanged);

    m_pCardIDComboBox = new QComboBox;
    m_pCardIDComboBox->setView(new QListView);
    m_pCardIDComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pCardIDComboBox->setMaxVisibleItems(10);
    m_pCardIDComboBox->setStyle(QStyleFactory::create("Windows"));
    m_pCardIDComboBox->setFixedSize(400, 50);
    connect(m_pCardIDComboBox, SIGNAL(currentTextChanged(const QString &)), this, SLOT(_SlotCardIDComboBoxChanged(const QString &)));

    m_pStackedWidget = new QStackedWidget;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        CMeltingOneWidget *pMeltingWidget = new CMeltingOneWidget(i);
        connect(pMeltingWidget, &CMeltingOneWidget::SignalShowCurrentTestCardID, this, &CMeltingCurve::_SlotShowCurrentTestCardID);
        m_pMeltingWidgetList.push_back(pMeltingWidget);
        m_pStackedWidget->addWidget(pMeltingWidget);
    }
}

void CMeltingCurve::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->addSpacing(10);
    pTopLayout->setSpacing(10);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addSpacing(30);
    pTopLayout->addWidget(m_pCardIDLineEdit);
    pTopLayout->addWidget(m_pCardIDComboBox);
    pTopLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addSpacing(10);
    pLayout->addLayout(pTopLayout);
    pLayout->addWidget(m_pStackedWidget);
    this->setLayout(pLayout);
}

void CMeltingCurve::_ShowLast100MeltingID()
{
    QStringList strList = CHistoryDB::GetInstance()->getLastMeltingFLID(100);
    if(strList.size() > 0)
    {
        m_pCardIDComboBox->clear();
        m_pCardIDComboBox->addItems(strList);
        m_pCardIDComboBox->activated(strList.first());
    }

}
