﻿#include "CWiFiWidget.h"
#include <QDebug>
#include <QTimer>
#include <QProcess>

#include "CWiFiThread.h"
#include "CWiFiItemWidget.h"

#include "CConfigJson.h"
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

#define MAX_NUM_WIFI 30

CWiFiWidget::CWiFiWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    m_pInputWidget = new CWiFiInputWidget(parent);
    connect(m_pInputWidget, &CWiFiInputWidget::SignalConnect, this, &CWiFiWidget::_SlotConnectWiFi);
    m_pInputWidget->setVisible(false);

    qRegisterMetaType<QMap<QString,QString>>("QMap<QString,QString>");

    m_strTipsText = tr("提示");
    m_bShow = false;
    m_bConnecting = false;
    m_bManualOpen = false;
    m_bManualClose = false;
    m_bScanning = false;

    m_iReConnectTimes = 0;
    m_pReConnectTimer = new QTimer(this);
    connect(m_pReConnectTimer, &QTimer::timeout, this, &CWiFiWidget::_SlotReConnectTimer);
    connect(this, &CWiFiWidget::SignalWiFiAbort, this, &CWiFiWidget::_SlotWiFiAbort);

    m_pScanTimer = new QTimer(this);
    connect(m_pScanTimer, &QTimer::timeout, this, &CWiFiWidget::_SlotScanTimer);

    CWiFiThread::GetInstance();
    connect(CWiFiThread::GetInstance(), &CWiFiThread::SignalScanMap, this, &CWiFiWidget::SlotScanMap);
    connect(CWiFiThread::GetInstance(), &CWiFiThread::SignalConnectEnd, this, &CWiFiWidget::SlotWiFiConnectEnd);

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalAppStartEnd, this, &CWiFiWidget::SlotAppStartEnd);

    QTimer::singleShot(2000, this, &CWiFiWidget::_SlotReadCfg);
}

void CWiFiWidget::_SlotReadCfg()
{
    QJsonObject qNetworkObj = CConfigJson::GetInstance()->GetConfigJsonObject("Network");
    m_strCurrentName = qNetworkObj.value("wifi_name").toString();
    m_strCurrentPwd = qNetworkObj.value("wifi_pwd").toString();
    qDebug()<<"软件上次连接WiFi:"<<m_strCurrentName<<m_strCurrentPwd;

    if(false == qNetworkObj.contains("wifi_or_eth"))
    {
        System("ifconfig wlan0 down");
        m_pCloseBtn->setChecked(true);
        return;
    }

    int iNetwork = qNetworkObj.value("wifi_or_eth").toInt();
    if(eNetwork_Wifi == iNetwork)
    {
        _SlotWiFiOpenBtn();
        m_pOpenBtn->setChecked(true);
    }
    else
    {
        System("ifconfig wlan0 down");
        m_pCloseBtn->setChecked(true);
    }
}

void CWiFiWidget::SlotAppStartEnd()
{
#ifndef __aarch64__
    return;
#endif

    std::thread wifithread(&CWiFiWidget::_Thread2CheckWiFi, this);
    wifithread.detach();

    std::thread eththread(&CWiFiWidget::_Thread2CheckEth, this);
    eththread.detach();
}

static bool CheckGatewayOK(QString strDev)
{
    QString strGateway = RunQProcess("route -n");
    QStringList strList = strGateway.split("\n");
    for(int i=0; i<strList.size(); i++)
    {
        QString strOneLine = strList.at(i);
        if(strOneLine.contains("UG") && strOneLine.contains(strDev))
            return true;
    }
    return false;
}

static bool IsWiFiOK()
{
    QString strWlanState = RunQProcess("iw dev wlan0 link");
    return strWlanState.contains("Connected to");
}

void CWiFiWidget::_Thread2CheckWiFi()
{
    while (1)
    {
        //只能作为没连上的判断;防止网络波动,连续3次断的才是断的
        bool bWiFiOK = true;
        for(int i=0; i<3; i++)
        {
            bWiFiOK = bWiFiOK & IsWiFiOK();
            QThread::msleep(300);
        }
        if(false == bWiFiOK)
        {
            emit CPublicConfig::GetInstance()->SignalWiFiStatus(false);
            CPublicConfig::GetInstance()->SetWiFiConnect(false);
            emit SignalWiFiAbort();
        }

        QThread::msleep(200);
    }
}

void CWiFiWidget::_Thread2CheckEth()
{
    while (1)
    {
        bool bEthOK = false;
        QString strEthState = RunQProcess("ifconfig");
        if(strEthState.contains("eth1"))
        {
            QString strEthShow = RunQProcess("ip addr show eth1");
            if(strEthShow.contains("inet"))
                bEthOK = CheckGatewayOK("eth1");
        }
        emit CPublicConfig::GetInstance()->SignalEth1Status(bEthOK);
        CPublicConfig::GetInstance()->SetEthConnect(bEthOK);

        QThread::msleep(1000);
    }
}

void CWiFiWidget::_SlotWiFiAbort()
{
//    static int iAbortTimes = 0;
//    iAbortTimes++;
//    if(iAbortTimes <= 3)
//        return;
//    iAbortTimes = 0;

//    qDebug()<<Q_FUNC_INFO<<m_bConnecting<<m_pInputWidget->isVisible()<<m_pCloseBtn->isChecked()
//           <<m_bManualClose<<m_strScanInfoList.isEmpty()<<m_strCurrentName.isEmpty()<<m_pReConnectTimer->isActive();
    //1.正在连接 2.用户正在手动操作 3.WiFi已关闭 4.手动断开 5.还没扫描
    if(m_bConnecting || m_pInputWidget->isVisible() || m_pCloseBtn->isChecked()
            || m_bManualClose || m_strScanInfoList.isEmpty() || m_strCurrentName.isEmpty())
    {
        return;
    }

    if(m_pReConnectTimer->isActive())
        return;

    _SetAllUnconnect();
    m_pReConnectTimer->start(60000);
}

void CWiFiWidget::_SlotReConnectTimer()
{
    m_iReConnectTimes++;
    for(int i=0; i<m_strScanInfoList.size(); i++)
    {
        if(m_strCurrentName == m_strScanInfoList.at(i).at(1))
        {
            qDebug()<<"WiFi已断开,正在重连:"<<m_iReConnectTimes<<m_strCurrentName<<m_strCurrentPwd;
            _ConnectWiFi(m_strCurrentName, m_strCurrentPwd);
            break;
        }
    }
}

void CWiFiWidget::_ConnectWiFi(QString strName, QString strPwd)
{
    //clear ip ui
    QStringList strList = {"...", "...", "..."};
    emit SignalWiFiIPInfoList(strList);

    m_bConnecting = true;
    _SetListWidgetItemStatus(strName, eWiFi_Connecting);
    CWiFiThread::GetInstance()->ConnectWiFi(strName, strPwd);
}

void CWiFiWidget::_SlotShowInputWidget(QString strName)
{
    if(m_bConnecting)
    {
        int iBtnType = ShowQuestion(this->parentWidget(), m_strTipsText, tr("WiFi %1 正在连接，确定断开此WiFi的连接吗").arg(m_strCurrentName));
        if(QMessageBox::Yes != iBtnType)
            return;

        m_bConnecting = false;
        CWiFiThread::GetInstance()->DisconnectWiFi();
    }

    m_pInputWidget->SetWiFiName(strName);
    m_pInputWidget->raise();
    m_pInputWidget->show();
    m_pInputWidget->activateWindow();
}

void CWiFiWidget::_SlotConnectWiFi(QString strName, QString strPwd)
{
    qDebug()<<"手动连接WiFi:"<<strName<<strPwd;
    m_iReConnectTimes = 0;
    m_pReConnectTimer->stop();

    QJsonObject qNetworkObj;
    qNetworkObj.insert("wifi_name", strName);
    qNetworkObj.insert("wifi_pwd", strPwd);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("Network", qNetworkObj);

    m_bManualOpen = true; //是手动打开WiFi的,如果WiFi连接失败要弹窗
    m_bManualClose = false;
    m_strCurrentName = strName;
    m_strCurrentPwd = strPwd;
    _ConnectWiFi(strName, strPwd);
}

void CWiFiWidget::_SlotDisconnectWiFi(QString strName)
{
    m_bConnecting = false;
    m_bManualOpen = false;
    m_bManualClose = true; //手动关闭,不再自动连接WiFi
    _SetListWidgetItemStatus(strName, eWiFi_Unconnect);
    CWiFiThread::GetInstance()->DisconnectWiFi();
    QStringList strList = {"...", "...", "..."};
    emit SignalWiFiIPInfoList(strList);
    RUN_LOG("手动断开WiFi,软件将不会自动重连WiFi");
}

void CWiFiWidget::_SetListWidgetItemStatus(QString strName, WiFiStatus eStatus)
{
    for(int iRow=0; iRow<m_pListWidget->count(); iRow++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(iRow);
        if(nullptr == pItem)
            return;

        CWiFiItemWidget *pWidget = (CWiFiItemWidget *)m_pListWidget->itemWidget(pItem);
        if(nullptr == pWidget)
            return;

        if(strName == pWidget->GetName())
        {
            pWidget->SetStatus(eStatus);
            pWidget->SetBtnVisable(true);
        }
        else
        {
            pWidget->SetStatus(eWiFi_Unconnect);
            pWidget->SetBtnVisable(false);
        }
    }
}

void CWiFiWidget::_SetAllUnconnect()
{
    for(int iRow=0; iRow<m_pListWidget->count(); iRow++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(iRow);
        if(nullptr == pItem)
            continue;

        CWiFiItemWidget *pWidget = (CWiFiItemWidget *)m_pListWidget->itemWidget(pItem);
        if(nullptr == pWidget)
            continue;

        pWidget->SetStatus(eWiFi_Unconnect);
    }
}

void CWiFiWidget::SlotWiFiConnectEnd(bool bConnectOK, QString strName, QString strPwd)
{
    qDebug()<<"WiFi连接结果:"<<strName<<strPwd<<bConnectOK;
    if(m_strCurrentName != strName)
    {
        qDebug()<<__FUNCTION__<<"不是当前操作WiFi:"<<strName<<m_strCurrentName;
        return;
    }

    if(m_pCloseBtn->isChecked())
    {
        m_bConnecting = false;
        m_bManualOpen = false;
        m_iReConnectTimes = 0;
        m_pReConnectTimer->stop();
        CWiFiThread::GetInstance()->CloseWiFi();
        emit CPublicConfig::GetInstance()->SignalWiFiStatus(false);
        CPublicConfig::GetInstance()->SetWiFiConnect(false);
        return;
    }

    m_bConnecting = false;
    emit CPublicConfig::GetInstance()->SignalWiFiStatus(bConnectOK);
    CPublicConfig::GetInstance()->SetWiFiConnect(bConnectOK);

    if(false == bConnectOK)
    {
        _SetListWidgetItemStatus(strName, eWiFi_Unconnect);
        if(m_bManualOpen) //手动连接的,失败要弹窗提示
        {
            m_bManualOpen = false;
            ShowInformation(this->parentWidget(), m_strTipsText, tr("WiFi %1 连接失败").arg(strName));
        }
        return;
    }

    m_bManualOpen = false;
    m_iReConnectTimes = 0;
    m_pReConnectTimer->stop();
    RUN_LOG(QString("WiFi连接成功:%1,%2").arg(strName).arg(strPwd));
    _SetListWidgetItemStatus(strName, eWiFi_Connected);

    QStringList strIPList = GetIPInfoList("wlan0");
    QString strGateway = GetGateway("wlan0");
    QStringList strInfoList = {"", "", strGateway};
    if(strIPList.size() >= 3)
    {
        strInfoList[0] = strIPList.at(0);
        strInfoList[1] = strIPList.at(1);
    }
    emit SignalWiFiIPInfoList(strInfoList);
}

void CWiFiWidget::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    QWidget::showEvent(pEvent);
}

void CWiFiWidget::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

static bool StrengthLessThan(const QStringList& strList1,const QStringList strList2)
{
    return strList1[2] < strList2[2];
}

static QString GetStrength(double dValue)
{
    QString strStrength;
    if(dValue < 60)
        strStrength = "1";
    if(dValue >= 60 && dValue < 77)
        strStrength = "2";
    if(dValue >= 77 && dValue < 88)
        strStrength = "3";
    if(dValue >= 88 && dValue < 100)
        strStrength = "4";
    if(dValue >= 100)
        strStrength = "5";

    return strStrength;
}

void CWiFiWidget::SlotScanMap(QMap<QString, QString> strScanMap)
{
    m_bScanning = false;
    m_pScanLabel->clear();
    m_pScanTimer->stop();

    if(m_pCloseBtn->isChecked())
        return;

    qDebug()<<"WiFi扫描结果:"<<strScanMap.size()<<strScanMap;
    if(strScanMap.isEmpty())
        return;

    m_strScanInfoList.clear();
    for(auto it=strScanMap.begin(); it!=strScanMap.end(); it++)
    {
        QString strName = it.key();
        QString strStrength = GetStrength(qAbs(it.value().toDouble()));

        QStringList strOneList = {0, strName, strStrength};
        m_strScanInfoList.push_back(strOneList);
    }

    std::sort(m_strScanInfoList.begin(), m_strScanInfoList.end(), StrengthLessThan);

    if(m_strScanInfoList.size() > MAX_NUM_WIFI)
        m_strScanInfoList = m_strScanInfoList.mid(0, MAX_NUM_WIFI);
    else
        _HideListWidgetItem(m_strScanInfoList.size());

    for(int i=0; i<m_strScanInfoList.size(); i++)
        m_strScanInfoList[i][0] = QString::number(i + 1);

    int iMin = qMin(m_strScanInfoList.size(), MAX_NUM_WIFI);
    for(int i=0; i<iMin; i++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(i);
        if(nullptr == pItem)
            continue;
        pItem->setHidden(false);

        CWiFiItemWidget *pWidget = (CWiFiItemWidget *)m_pListWidget->itemWidget(pItem);
        if(nullptr == pWidget)
            continue;
        pWidget->SetInfoList(m_strScanInfoList.at(i));
    }

    if(m_strCurrentName.isEmpty())
        return;

    for(int i=0; i<m_strScanInfoList.size(); i++)
    {
        if(m_strCurrentName == m_strScanInfoList.at(i).at(1))
        {
            qDebug()<<"扫描结束连接上次WiFi:"<<m_strCurrentName<<m_strCurrentPwd;
            _ConnectWiFi(m_strCurrentName, m_strCurrentPwd);
            break;
        }
    }
}

void CWiFiWidget::_HideListWidgetItem(int iStartIndex)
{
    if(iStartIndex < 0)
        return;

    for(int i=iStartIndex; i<MAX_NUM_WIFI; i++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(i);
        if(nullptr == pItem)
            continue;

        CWiFiItemWidget *pWidget = (CWiFiItemWidget *)m_pListWidget->itemWidget(pItem);
        if(nullptr == pWidget)
            continue;
        pWidget->SetStatus(eWiFi_Unconnect);
        pItem->setHidden(true);
    }
}

void CWiFiWidget::_SlotScanTimer()
{
    m_bScanning = false;
    m_pScanLabel->setText(tr("WiFi扫描超时"));
    m_pScanTimer->stop();

    if(m_bShow)
        ShowInformation(this->parentWidget(), m_strTipsText, tr("WiFi扫描超时"));
}

void CWiFiWidget::_SlotWiFiOpenBtn()
{
    if(m_bScanning)
        return;

    m_bScanning = true;
    m_pScanTimer->start(10000);
    m_pScanLabel->setText(tr("扫描中"));
    qDebug()<<Q_FUNC_INFO<<"打开WiFi";

    emit SignalWiFiOpen(true);
    CWiFiThread::GetInstance()->OpenWiFi();

    QJsonObject qNetworkObj;
    qNetworkObj.insert("wifi_or_eth", eNetwork_Wifi);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("Network", qNetworkObj);
}

void CWiFiWidget::_SlotWiFiCloseBtn()
{
    if(m_bScanning)
    {
        m_pOpenBtn->setChecked(true);
        ShowInformation(this->parentWidget(), m_strTipsText, tr("WiFi扫描中，请稍后再试"));
        return;
    }

    m_bScanning = false;
    m_pScanLabel->clear();
    m_pScanTimer->stop();
    qDebug()<<Q_FUNC_INFO<<"关闭WiFi";

    m_iReConnectTimes = 0;
    m_pReConnectTimer->stop();
    _HideListWidgetItem(0);
    emit SignalWiFiOpen(false);
    CWiFiThread::GetInstance()->CloseWiFi();
    emit CPublicConfig::GetInstance()->SignalWiFiStatus(false);
    CPublicConfig::GetInstance()->SetWiFiConnect(false);

    QJsonObject qNetworkObj;
    qNetworkObj.insert("wifi_or_eth", eNetwork_Null);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("Network", qNetworkObj);

    m_bConnecting = false;
    m_bManualOpen = false;
    m_bManualClose = false;
    m_strScanInfoList.clear();
    RUN_LOG("手动关闭WiFi,软件将不会自动重连WiFi");
}

void CWiFiWidget::_SlotListWidgetItemClicked(QListWidgetItem *pClickedItem)
{
    if(nullptr == pClickedItem)
        return;

    for(int iRow=0; iRow<m_pListWidget->count(); iRow++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(iRow);
        if(nullptr == pItem)
            return;

        CWiFiItemWidget *pWidget = (CWiFiItemWidget *)m_pListWidget->itemWidget(pItem);
        if(nullptr == pWidget)
            return;

        if(pClickedItem == pItem)
            pWidget->SetBtnVisable(true);
        else
            pWidget->SetBtnVisable(false);
    }
}

void CWiFiWidget::_InitWidget()
{
    m_pTitleWidget = new CHLabelTitleWidget(tr("WIFI设置"));

    m_pOpenBtn = new QRadioButton(tr("打开"));
    connect(m_pOpenBtn, &QRadioButton::clicked, this, &CWiFiWidget::_SlotWiFiOpenBtn);

    m_pCloseBtn = new QRadioButton(tr("关闭"));
    connect(m_pCloseBtn, &QRadioButton::clicked, this, &CWiFiWidget::_SlotWiFiCloseBtn);
    m_pCloseBtn->setChecked(true);

    m_pScanLabel = new QLabel(this);

    m_pListWidget = new QListWidget;
    m_pListWidget->setFixedSize(776, 82*7);
    m_pListWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pListWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    connect(m_pListWidget, &QListWidget::itemClicked, this, &CWiFiWidget::_SlotListWidgetItemClicked);

    for(int i=0; i<MAX_NUM_WIFI; i++)
    {
        QStringList strInfoList = {QString::number(i + 1), "", "3"};
        CWiFiItemWidget *pWidget = new CWiFiItemWidget(strInfoList);
        connect(pWidget, &CWiFiItemWidget::SignalConnect, this, &CWiFiWidget::_SlotShowInputWidget);
        connect(pWidget, &CWiFiItemWidget::SignalDisconnect, this, &CWiFiWidget::_SlotDisconnectWiFi);

        QListWidgetItem *pItem = new QListWidgetItem(m_pListWidget);
        pItem->setSizeHint(QSize(776, 82));
        m_pListWidget->setItemWidget(pItem, pWidget);
        pItem->setHidden(true);
    }
}

void CWiFiWidget::_InitLayout()
{
    QHBoxLayout *pRadioLayout = new QHBoxLayout;
    pRadioLayout->setMargin(0);
    pRadioLayout->setSpacing(80);
    pRadioLayout->addWidget(m_pOpenBtn);
    pRadioLayout->addWidget(m_pCloseBtn);
    pRadioLayout->addSpacing(20);
    pRadioLayout->addWidget(m_pScanLabel);
    pRadioLayout->addStretch(1);

    QVBoxLayout *pWiFiLayout = new QVBoxLayout;
    pWiFiLayout->setContentsMargins(18, 0, 0, 0);
    pWiFiLayout->addLayout(pRadioLayout);
    pWiFiLayout->addSpacing(24);
    pWiFiLayout->addWidget(m_pListWidget);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(24);
    pLayout->addLayout(pWiFiLayout);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
