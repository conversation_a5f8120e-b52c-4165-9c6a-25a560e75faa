#include "CFaultLog.h"
#include <QDir>
#include <QDebug>
#include <QListView>
#include <QBoxLayout>
#include <QHeaderView>

#include "CLogDB.h"
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CShowLogDetail.h"
#include "CReadWriteXlsxThread.h"
#include "CRunTest.h"

extern bool G_RegisterMode;

CFaultLog::CFaultLog(QWidget *parent) : QWidget(parent)
{
    this->setFixedSize(1494, 798);
    _InitWidget();
    _InitLayout();

    m_pExportBar = new CBusyProgressBar(m_strTipsText, tr("正在导出故障日志"), this);
    m_pExportBar->setVisible(false);

    m_bRefresh = false;
    m_bShow = false;
    m_bQueryLike = false;
    m_iOnePageLines = 12;
    _ShowAll();

    for(int i=0; i<gk_iMachineCount; i++)
        m_qCurrentTestLogList << QList<QVariantList>();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTimingTestStart,
            this, &CFaultLog::SlotTimingTestStart);

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSaveFaultCode,
            this, &CFaultLog::SlotSaveFaultCode);

    Register2Map(Method_notify);
    Register2Map(Method_start);
}

CFaultLog::~CFaultLog()
{
    UnRegister2Map(Method_notify);
    UnRegister2Map(Method_start);
}

void CFaultLog::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_notify == iMethodID)
    {
        QVariantMap varMap = qVarData.toMap();
        int iCode = varMap.value("code", -1).toInt();
        if(iCode < 0)
            return;

        SlotSaveFaultCode(iCode, iMachineID, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    }
    else if(Method_start == iMethodID)
    {
        _TestEnd(iMachineID);
    }
}

void CFaultLog::SlotTimingTestStart(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= m_qCurrentTestLogList.size())
        return;

    qDebug()<<QString("时序开始,清空%1#当前保存故障日志").arg(iMachineID + 1);
    m_qCurrentTestLogList[iMachineID].clear();
}

void CFaultLog::SlotUpdateCodeInfoMap(const QMap<int, QStringList> &map)
{
    m_iCodeInfoMap = map;
    qDebug()<<Q_FUNC_INFO<<"更新故障码map:"<<map.size();
}

void CFaultLog::SlotSaveFaultCode(int iCode, int iMachineID, const QString &strTime)
{
    if(!m_iCodeInfoMap.contains(iCode))
    {
        qDebug()<<"此故障码不存在:"<<iCode;
        return;
    }

    QString strMachine = QString("%1#").arg(iMachineID + 1);
    if(99 == iMachineID)
        strMachine = tr("上位机");

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);

    QStringList strList = m_iCodeInfoMap.value(iCode);
    qDebug()<<Q_FUNC_INFO<<strList;
    int iLevel = strList.at(FaultCode_Level).toInt();

    if(false == G_RegisterMode)
    {
        if(iLevel >= 4 && sRunInfo.bRunning)
        {
            sRunInfo.bRunning = false;
            qDebug()<<QString("%1#故障码%2等级大于等于5将停止测试").arg(iMachineID + 1).arg(iCode);
            QString strCmd = GetJsonCmdString(Method_stop);
            SendJsonCmd(iMachineID, Method_stop, strCmd);
        }
    }

    if(false == G_RegisterMode)
    {
        if(iLevel >= 4 && sRunInfo.iRunTimes <= 1)
        {
            QString strUserDesc = strList.at(FaultCode_UserDesc);
            strUserDesc.replace('\r', ' ');
            strUserDesc.replace('\n', ' ');
            strUserDesc.replace('\t', ' ');
            strUserDesc = QString("%1#, %2: ").arg(iMachineID + 1).arg(iCode) + strUserDesc;
            ShowError((QWidget*)gk_pMainWindow, m_strTipsText, strUserDesc); //全屏弹窗
        }
    }

    strList.pop_front();
    strList.push_front(strTime);
    strList.insert(3, strMachine);
    strList.push_back("");
    qDebug()<<"添加故障日志:"<<strList.size()<<strList;
    CLogDB::instance().AddFaultLog(strList);
    emit CPublicConfig::GetInstance()->SignalRefreshSystemFalutLogPage();

    if(iMachineID >= 0 && iMachineID < m_qCurrentTestLogList.size())
    {
        QVariantList qOneList;
        qOneList << m_qCurrentTestLogList.at(iMachineID).size() + 1;
        for(int i=0; i<strList.size()- 1; i++)
            qOneList << strList.at(i);
        qDebug()<<qOneList;
        m_qCurrentTestLogList[iMachineID].push_back(qOneList);

        //传给自检
        emit CPublicConfig::GetInstance()->SignalFaultLog(iMachineID, qOneList);
    }

    m_bRefresh = true;
    if(!m_bShow || 0 != m_iCurrentPage || m_bHasQuery)
        return;

    m_bRefresh = false;
    _ShowAll();
}

void CFaultLog::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    m_iOnePageLines = (m_pTableWidget->height() - 50) / 50;
    if(m_bRefresh)
    {
        if(0 == m_iCurrentPage && !m_bHasQuery)
        {
            m_bRefresh = false;
            _ShowAll();
        }
    }

    QWidget::showEvent(pEvent);
}

void CFaultLog::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CFaultLog::resizeEvent(QResizeEvent *pEvent)
{
    //m_iOnePageLines = (m_pTableWidget->height() - 50) / 50;
    //qDebug()<<Q_FUNC_INFO<<m_iOnePageLines<<m_pTableWidget->height();
    QWidget::resizeEvent(pEvent);
}

void CFaultLog::_SlotListBtn()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: _QueryLog();   break;
    case 1: _ShowAll();    break;
    case 2: _ShowDetail(); break;
    case 3: _ExportLog();  break;
    case 4: _ClearAll();   break;
    default: break;
    }
}

void CFaultLog::_QueryLog()
{
    m_strQueryParam = m_pQueryLineEdit->text().trimmed();
    if(m_strQueryParam.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请输入要查询的内容"));
        return;
    }

    int index = m_pQueryComboBox->currentIndex();
    m_strQueryType.clear();
    QStringList strList = {"id", "Date", "FaultCode", "FaultLevel", "Machine", "FaultUnit",
                           "FaultDescribe", "FactoryDescribe", "FaultHandle"};
    if(index >= 0 && index < strList.size())
        m_strQueryType = strList.at(index);

    m_bHasQuery = true;
    m_iCurrentPage = 0;

    m_bQueryLike = true;
    if(0 == index || 2 == index || 3 == index)
        m_bQueryLike = false;

    m_iTotalLines = CLogDB::instance().GetFaultLogQueryRecordCount(m_strQueryType, m_strQueryParam, m_bQueryLike);
    m_iTotalPages = m_iTotalLines / m_iOnePageLines;
    m_iLeftLines = m_iTotalLines % m_iOnePageLines;
    if(0 != m_iLeftLines)
        m_iTotalPages++;
    qDebug()<<"故障日志查询内容:"<<m_strQueryType<<m_strQueryParam
           <<",查询结果总数:"<<m_iTotalLines<<",页数:"<<m_iTotalPages
          <<",当前页数:"<<m_iCurrentPage<<",每页数量:"<<m_iOnePageLines;
    _ShowCurrentPageQueryData();
}

void CFaultLog::_ShowAll()
{
    m_bHasQuery = false;
    m_iCurrentPage = 0;

    m_iTotalLines = CLogDB::instance().GetFaultLogAllRecordCount();
    m_iTotalPages = m_iTotalLines / m_iOnePageLines;
    m_iLeftLines = m_iTotalLines % m_iOnePageLines;
    if(0 != m_iLeftLines)
        m_iTotalPages++;
    qDebug()<<"故障日志总数:"<<m_iTotalLines<<",页数:"<<m_iTotalPages
           <<",当前页数:"<<m_iCurrentPage<<",每页数量:"<<m_iOnePageLines;
    _ShowCurrentPageAllData();
}

void CFaultLog::_ShowDetail()
{
    int iRow = m_pTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择一行"));
        return;
    }

    QStringList strDataList;
    for(int i=0; i<m_strTitleList.size(); i++)
    {
        QTableWidgetItem *pItem = m_pTableWidget->item(iRow, i);
        if(pItem)
        {
            QString strOneLine = QString("%1: %2").arg(m_strTitleList.at(i)).arg(pItem->text());
            strDataList.push_back(strOneLine);
        }
    }
    CShowLogDetail *pShow = new CShowLogDetail(this);
    pShow->ShowTextList(strDataList);
}

void CFaultLog::_ExportLog()
{
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    if(!UDiskExistAndCreateDir(strExportDir, this))
        return;

    m_pExportBar->setVisible(true);

    std::thread mythread(&CFaultLog::_Thread2Export, this);
    mythread.detach();
}

void CFaultLog::_Thread2Export()
{
    QString strSN = CPublicConfig::GetInstance()->GetMachineSN();
    strSN = DeleteSpecialCharacters(strSN);
    QString strTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strXlsxName = QString("faultlog_%1_%2.xlsx").arg(strSN).arg(strTime);

    STXlsxParmasStruct* pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "faultlog";
    pXlsxStruct->strTitleList = m_strTitleList;

    QList<QStringList> strAllList;
    CLogDB::instance().RealAllFaultLogData(strAllList);

    for(int i=0; i<strAllList.size(); i++)
    {
        QVariantList qVarList;
        for(int j=0; j<strAllList.at(i).size();j++)
            qVarList.push_back(strAllList.at(i).at(j));

        pXlsxStruct->varWriteDataList.push_back(qVarList);
    }

    FunWriteXlsxEndCallBack lambdaFunction = [this](QString strXlsxName, QString strTableName)
    {
        Q_UNUSED(strTableName);

        QString strDestPath = CPublicConfig::GetInstance()->GetUDiskExportDir();
        bool bCopy = CopyQFileDir(strXlsxName, QDir(strDestPath));
        this->m_pExportBar->setVisible(false);
        ExportEndUmountUSB();
        if(bCopy)
           ShowSuccess(this, m_strTipsText, tr("故障日志导出完成"));
        else
           ShowError(this, m_strTipsText, tr("故障日志导出失败，请检查U盘"));
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CFaultLog::_ClearAll()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定清空所有故障日志吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(0);
    CLogDB::instance().DeleteAllFaultLogAndResetID();
    _ShowAll();
    emit CPublicConfig::GetInstance()->SignalRefreshSystemFalutLogPage();
}

void CFaultLog::_TestEnd(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= m_qCurrentTestLogList.size())
        return;

    //自检不生成xlsx
    QString strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(iMachineID);
    if(strXlsxName.isEmpty())
        return;

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "fault_log";
    pXlsxStruct->strTitleList = m_strTitleList;

    pXlsxStruct->varWriteDataList = m_qCurrentTestLogList.at(iMachineID);
    pXlsxStruct->bAutoAdjustCol = true;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    qDebug()<<QString("%1# fault log write to result xlsx end").arg(iMachineID + 1);
}

void CFaultLog::_SlotPrePageBtn()
{
    if(m_iCurrentPage <= 0)
        return;

    m_iCurrentPage--;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();
}

void CFaultLog::_SlotNextPageBtn()
{
    if(m_iCurrentPage + 1 >= m_iTotalPages)
        return;

    m_iCurrentPage++;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();
}

void CFaultLog::_SlotGotoPageBtn()
{
    QString strPage = m_pGotoLineEdit->text();
    if(strPage.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请输入要跳转到的页数"));
        return;
    }

    int iPage = strPage.toInt();
    if(iPage <= 0)
        iPage = 1;
    if(iPage >= m_iTotalPages)
        iPage = m_iTotalPages;
    m_pGotoLineEdit->setText(QString::number(iPage));

    m_iCurrentPage = iPage - 1;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();
}

void CFaultLog::_ShowCurrentPageAllData()
{
    QList<QStringList> strReadList;
    CLogDB::instance().GetOnePageFaultLog(m_iCurrentPage, m_iOnePageLines, strReadList);
    _UpdateTableWidget(strReadList);
    _UpdateGroupBoxInfo();
}

void CFaultLog::_ShowCurrentPageQueryData()
{
    QList<QStringList> strReadList;
    CLogDB::instance().GetQueryOnePageFaultLog(m_strQueryType, m_strQueryParam,
                                               m_iCurrentPage, m_iOnePageLines,
                                               m_bQueryLike, strReadList);
    _UpdateTableWidget(strReadList);
    _UpdateGroupBoxInfo();
}

void CFaultLog::_UpdateTableWidget(const QList<QStringList> &strList)
{
    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(strList.size());
    for(int iRow=0; iRow<strList.size(); iRow++)
    {
        QStringList oneRowList = strList.at(iRow);
        //int iID = m_iTotalLines - m_iCurrentPage * m_iOnePageLines - iRow;
        //oneRowList[0] = QString::number(iID);
        int iSize = qMin(oneRowList.size(), m_strTitleList.size());
        for(int iCol=0; iCol<iSize; iCol++)
        {
            QTableWidgetItem *pItem = new QTableWidgetItem;
            pItem->setText(oneRowList.at(iCol));
            pItem->setTextAlignment(Qt::AlignCenter);
            m_pTableWidget->setItem(iRow, iCol, pItem);
        }
    }
}

void CFaultLog::_UpdateGroupBoxInfo()
{
    m_pLinesLabel->setText(tr("总共%1条记录").arg(m_iTotalLines));
    m_pPageLabel->setText(QString("%1/%2").arg(m_iCurrentPage + 1).arg(m_iTotalPages));

    if(m_iCurrentPage <= 0)
    {
        m_pPrePageBtn->setEnabled(false);
        m_pNextPageBtn->setEnabled(true);
    }
    else if(m_iCurrentPage >= m_iTotalPages - 1)
    {
        m_pPrePageBtn->setEnabled(true);
        m_pNextPageBtn->setEnabled(false);
    }
    else
    {
        m_pPrePageBtn->setEnabled(true);
        m_pNextPageBtn->setEnabled(true);
    }

    if(m_iTotalLines <= m_iOnePageLines)
    {
        m_pPageLabel->setText("1/1");
        m_pPrePageBtn->setEnabled(false);
        m_pNextPageBtn->setEnabled(false);
    }
}

void CFaultLog::_InitWidget()
{
    QStringList strTitleList = {tr("序号"), tr("日期"), tr("故障码"), tr("故障等级"), tr("仪器"),
                                tr("故障单元"), tr("故障描述"), tr("工程描述"),tr("故障处理")};
    m_strTitleList = strTitleList;
    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 80);
    pHorizontalHeader->resizeSection(1, 200);
    pHorizontalHeader->resizeSection(2, 100);
    pHorizontalHeader->resizeSection(3, 100);
    pHorizontalHeader->resizeSection(4, 100);
    pHorizontalHeader->resizeSection(5, 100);
    pHorizontalHeader->setSectionResizeMode(6, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(7, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(8, QHeaderView::Stretch);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);

    m_pGroupBox = _CreatePageGroupBox();

    m_pQueryComboBox = new QComboBox;
    m_pQueryComboBox->setView(new QListView);
    m_pQueryComboBox->setFixedSize(120, 50);
    m_pQueryComboBox->addItems(strTitleList);

    m_pQueryLineEdit = new CLineEdit;
    m_pQueryLineEdit->setFixedSize(100, 50);
    m_pQueryLineEdit->setPlaceholderText(tr("查询内容"));

    int iBtnWidth = 100;
    if(eLanguage_Spanish == gk_iLanguage)
        iBtnWidth = 120;
    else if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 140;
    else if(eLanguage_Italian == gk_iLanguage)
        iBtnWidth = 210;

    QStringList strBtnList = {tr("查询"), tr("刷新"), tr("详情"), tr("导出"), tr("清空")};
    for(int i=0; i<strBtnList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strBtnList.at(i));
        pBtn->setFixedSize(iBtnWidth, 50);
        pBtn->setProperty("index", i);
        m_pBtnList.push_back(pBtn);
        connect(pBtn, &QPushButton::clicked, this, &CFaultLog::_SlotListBtn);
    }
}

void CFaultLog::_InitLayout()
{
    QHBoxLayout *pBottomLayout = new QHBoxLayout;
    pBottomLayout->setMargin(0);
    pBottomLayout->setSpacing(15);
    pBottomLayout->addWidget(m_pQueryComboBox);
    pBottomLayout->addWidget(m_pQueryLineEdit);
    for(int i=0; i<m_pBtnList.size(); i++)
        pBottomLayout->addWidget(m_pBtnList.at(i));
    pBottomLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->setSpacing(0);
    pMainLayout->addWidget(m_pTableWidget);
    pMainLayout->addWidget(m_pGroupBox);
    pMainLayout->addSpacing(10);
    pMainLayout->addLayout(pBottomLayout);
    this->setLayout(pMainLayout);
}

QGroupBox *CFaultLog::_CreatePageGroupBox()
{
    m_pPrePageBtn = new QPushButton;
    m_pPrePageBtn->setFixedSize(50, 50);
    m_pPrePageBtn->setObjectName("PrePageBtn");
    connect(m_pPrePageBtn, &QPushButton::clicked, this, &CFaultLog::_SlotPrePageBtn);

    m_pPageLabel = new QLabel("0/0");
    m_pPageLabel->setFixedHeight(50);
    m_pPageLabel->setMinimumWidth(80);
    m_pPageLabel->setAlignment(Qt::AlignCenter);

    m_pNextPageBtn = new QPushButton;
    m_pNextPageBtn->setFixedSize(50, 50);
    m_pNextPageBtn->setObjectName("NextPageBtn");
    connect(m_pNextPageBtn, &QPushButton::clicked, this, &CFaultLog::_SlotNextPageBtn);

    m_pGotoLineEdit = new CLineEdit;
    m_pGotoLineEdit->setFixedSize(80, 50);
    m_pGotoLineEdit->setAlignment(Qt::AlignCenter);
    m_pGotoLineEdit->setInputMethodHints(Qt::ImhDigitsOnly);

    m_pGotoPageBtn = new QPushButton(tr("跳转"));
    m_pGotoPageBtn->setFixedSize(80, 50);
    connect(m_pGotoPageBtn, &QPushButton::clicked, this, &CFaultLog::_SlotGotoPageBtn);

    m_pLinesLabel = new QLabel(tr("总共0条记录"));
    m_pLinesLabel->setFixedHeight(50);
    m_pLinesLabel->setMinimumWidth(120);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(20);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pPrePageBtn);
    pLayout->addWidget(m_pPageLabel);
    pLayout->addWidget(m_pNextPageBtn);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pGotoLineEdit);
    pLayout->addWidget(m_pGotoPageBtn);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pLinesLabel);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedHeight(80);
    pGroupBox->setObjectName("LogGroupBox");
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
