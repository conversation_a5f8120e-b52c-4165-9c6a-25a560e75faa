#ifndef CSELFTESTDEVITEMWIDGET_H
#define CSELFTESTDEVITEMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-05-23
  * Description: 自检单个设备
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QLabel>
#include <QGroupBox>
#include <QPushButton>

#include "PublicParams.h"
#include "CSelfTestMotor.h"

class CSelfTestDevItemWidget : public QWidget
{
    Q_OBJECT
public:
    CSelfTestDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent = nullptr);

    int GetMachineID() const;
    void ReceiveSelfTestResult(int iResult);
    void UpdateDateLabel(QString strLastDate, QString strNextDate);

public slots:
    void SlotSetDevStatus(int iMachineID, DeviceStatus eStatus);
    void SlotFaultLog(int iMachineID, QVariantList qLogList);

signals:
    void SignalStartSelfTest(int iMachineID, QString strXlsxPath);
    void SignalStopSelfTest(int iMachineID, bool bStop); //是否需要停止

private slots:
    void _SlotSelfTestBtn();
    void _SlotRunTimeout();

private:
    void _SelfTestResult(int iResult);    
    void _WriteFaultLogXlsx();
    void _WriteOtherXlsx();

private:
    QGroupBox *_CreateGroupBox();

private:
    QLabel *m_pTitleLabel;
    QLabel *m_pIndexLabel, *m_pTextLabel;
    QLabel *m_pLastNameLabel, *m_pLastDateLabel;
    QLabel *m_pNextNameLabel, *m_pNextDateLabel;
    QPushButton *m_pCaliBtn;

private:
    int m_iMachineID;
    SDevParamsStruct m_sDevParams;
    DeviceStatus m_eStatus;
    const QString m_strTipsText;
    QTimer *m_pRunTimer;
    QString m_strInfoXlsxPath, m_strUserXlsxPath; //详细表和普通表
    QDateTime m_qBeginTime;
    QList<QVariantList> m_qFaultLogList; //故障日志
};

#endif // CSELFTESTDEVITEMWIDGET_H
