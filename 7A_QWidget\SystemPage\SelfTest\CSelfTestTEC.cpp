#include "CSelfTestTEC.h"

#include <QDebug>

#include "CRunTest.h"
#include "CTimingTecDB.h"
#include "PublicConfig.h"
#include "CReadWriteXlsxThread.h"

CSelfTestTEC::CSelfTestTEC(QObject *parent) : QObject(parent)
{
    Register2Map(Method_pcr_tec_table_end);
    Register2Map(Method_pcr_info);
    Register2Map(Method_pcr_signal);

    _CheckTECExist();

    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_pInfoStructList.push_back(new SSelfTestInfoStruct());
    }
}

CSelfTestTEC::~CSelfTestTEC()
{
    UnRegister2Map(Method_pcr_tec_table_end);
    UnRegister2Map(Method_pcr_info);
    UnRegister2Map(Method_pcr_signal);

    for(int i=0; i<m_pInfoStructList.size(); i++)
    {
        SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_pInfoStructList.clear();
}

void CSelfTestTEC::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);

    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(!pStruct->bStart)
        return;

    if(Method_pcr_tec_table_end == iMethodID)
    {
        qDebug()<<QString("%1#TEC自检,启动PCR").arg(iMachineID + 1);
        QString strCmd = GetJsonCmdString(Method_pcr_start);
        SendJsonCmd(iMachineID, Method_pcr_start, strCmd);
    }
    else if(Method_pcr_info == iMethodID)
    {
        _HandlePCRInfo(iMachineID, qVarData);
    }
    else if(Method_pcr_signal == iMethodID)
    {
        _HandlePCRSignal(iMachineID, qVarData);
    }
}

void CSelfTestTEC::StartSelfTest(int iMachineID, QString strXlsxPath)
{
    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    m_sParamsStruct = CPublicConfig::GetInstance()->GetSelfTestParamsStruct().sTECStruct;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    pStruct->Clear();
    pStruct->bStart = true;
    pStruct->strXlsxPath = strXlsxPath;

    qDebug()<<QString("%1#开始TEC自检:").arg(iMachineID + 1)<<m_strTECName<<m_sParamsStruct.dStartTemp
           <<m_sParamsStruct.dMaxTemp<<m_sParamsStruct.dEndTemp<<m_sParamsStruct.dUpRate
          <<m_sParamsStruct.dDownRate<<m_sParamsStruct.dSpan;

    CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).strTimingData.clear();
    emit CPublicConfig::GetInstance()->SignalTecTestStart(iMachineID, m_strTECName);
}

void CSelfTestTEC::EndSelfTest(int iMachineID)
{
    qDebug()<<Q_FUNC_INFO<<iMachineID;
    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(pStruct->bStart)
    {
        QString strCmd = GetJsonCmdString(Method_pcr_stop);
        SendJsonCmd(iMachineID, Method_pcr_stop, strCmd);
    }

    pStruct->Clear();
}

void CSelfTestTEC::_CheckTECExist()
{
    m_strTECName = "tecSelfTest";
    m_strTecData = "TEC_SET_PARAM,1,50;TEC_SET_PARAM,2,300;TEC_SIN_RAMP_TO,4500,;TEC_HOLD_TIME,60000,;"
                   "TEC_SIN_RAMP_TO,9500,;TEC_HOLD_TIME,60000,;TEC_SIN_RAMP_TO,4500,;TEC_HOLD_TIME,60000,";

    if(!CTimingTecDB::GetInstance().IsTecExist(m_strTECName))
    {
        qDebug()<<"添加TEC自检时序:"<<m_strTECName<<m_strTecData;
        CTimingTecDB::GetInstance().AddOneTec(m_strTECName, m_strTecData);
    }
}

void CSelfTestTEC::_HandlePCRInfo(int iMachineID, const QVariant &qVarData)
{
    QVariantMap qVarMap = qVarData.toMap();
    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(pStruct->dTemp1Vec.isEmpty())
    {
        pStruct->qBeginTime = QDateTime::currentDateTime();
        pStruct->dTimeVec.push_back(0);
    }
    else
    {
        double dTime = pStruct->qBeginTime.msecsTo(QDateTime::currentDateTime());
        pStruct->dTimeVec.push_back(dTime / 1000.0);
    }

    double dM1 = qVarMap.value("Module1Temp").toDouble() / 100.0;
    double dM2 = qVarMap.value("Module2Temp").toDouble() / 100.0;
    pStruct->dTemp1Vec.push_back(dM1);
    pStruct->dTemp2Vec.push_back(dM2);
}

void CSelfTestTEC::_HandlePCRSignal(int iMachineID, const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.isEmpty())
        return;

    int iSignal = qVarList.at(0).toInt();
    if(0 != iSignal)
        return;

    _CheckTempData(iMachineID, 0);
    _CheckTempData(iMachineID, 1);

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    int iResult = -1;
    if(0 == pStruct->iResult1 && 0 == pStruct->iResult2)
        iResult = 0;

    pStruct->bStart = false;
    _WriteXlsx(iMachineID);
    qDebug()<<QString("%1#TEC自检结束,%2").arg(iMachineID + 1).arg(0 == iResult ? "成功" : "失败");
    emit SignalSelfTestResult(iMachineID, iResult);
}

void CSelfTestTEC::_CheckTempData(int iMachineID, int index)
{
    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    QString strLog = QString("%1#TEC自检,模组%2,").arg(iMachineID + 1).arg(index + 1);
    QVector<double> dTempVec;
    if(0 == index)
        dTempVec = pStruct->dTemp1Vec;
    else
        dTempVec = pStruct->dTemp2Vec;

    qDebug()<<QString("%1温度数据:").arg(strLog)<<dTempVec;

    int iUPMaxIndex = -1; //升温,第1个95±0.2
    for(int i=0; i<dTempVec.size(); i++)
    {
        double dTemp = dTempVec.at(i);
        double dMax = m_sParamsStruct.dMaxTemp + m_sParamsStruct.dSpan;
        double dMin = m_sParamsStruct.dMaxTemp - m_sParamsStruct.dSpan;
        if(dTemp >= dMin && dTemp <= dMax)
        {
            iUPMaxIndex = i;
            break;
        }
    }
    QString strMaxRange = QString("%1±%2").arg(m_sParamsStruct.dMaxTemp).arg(m_sParamsStruct.dSpan);
    qDebug()<<QString("%1升温最大温度%2下标:%3").arg(strLog).arg(strMaxRange).arg(iUPMaxIndex);
    if(-1 == iUPMaxIndex)
    {
        pStruct->strDetails += QString("模组%1#温度没有达到%2;").arg(index + 1).arg(strMaxRange);
        qDebug()<<QString("%1温度没有达到%2,自检失败").arg(strLog).arg(strMaxRange);
        if(0 == index)
        {
            pStruct->iResult1 = -1;
            emit CPublicConfig::GetInstance()->SignalSaveFaultCode(801, iMachineID);
        }
        else
        {
            pStruct->iResult2 = -1;
            emit CPublicConfig::GetInstance()->SignalSaveFaultCode(802, iMachineID);
        }
        return;
    }

    int iStartIndex = -1; //升温,最后1个45±0.2
    for(int i=iUPMaxIndex; i>=0; i--)
    {
        double dTemp = dTempVec.at(i);
        double dMax = m_sParamsStruct.dStartTemp + m_sParamsStruct.dSpan;
        double dMin = m_sParamsStruct.dStartTemp - m_sParamsStruct.dSpan;
        if(dTemp >= dMin && dTemp <= dMax)
        {
            iStartIndex = i;
            break;
        }
    }
    QString strStartRange = QString("%1±%2").arg(m_sParamsStruct.dStartTemp).arg(m_sParamsStruct.dSpan);
    qDebug()<<QString("%1起始温度%2下标:%3").arg(strLog).arg(strStartRange).arg(iStartIndex);

    int iDownMaxIndex = -1; //降温,最后1个95±0.2
    for(int i=dTempVec.size()-1; i>=0; i--)
    {
        double dTemp = dTempVec.at(i);
        double dMax = m_sParamsStruct.dMaxTemp + m_sParamsStruct.dSpan;
        double dMin = m_sParamsStruct.dMaxTemp - m_sParamsStruct.dSpan;
        if(dTemp >= dMin && dTemp <= dMax)
        {
            iDownMaxIndex = i;
            break;
        }
    }
    qDebug()<<QString("%1降温最大温度%2下标:%3").arg(strLog).arg(strMaxRange).arg(iDownMaxIndex);

    int iEndIndex = -1; //降温,第1个45±0.2
    for(int i=iDownMaxIndex; i<dTempVec.size(); i++)
    {
        double dTemp = dTempVec.at(i);
        double dMax = m_sParamsStruct.dEndTemp + m_sParamsStruct.dSpan;
        double dMin = m_sParamsStruct.dEndTemp - m_sParamsStruct.dSpan;
        if(dTemp >= dMin && dTemp <= dMax)
        {
            iEndIndex = i;
            break;
        }
    }
    QString strEndRange = QString("%1±%2").arg(m_sParamsStruct.dEndTemp).arg(m_sParamsStruct.dSpan);
    qDebug()<<QString("%1结束温度%2下标:%3").arg(strLog).arg(strEndRange).arg(iEndIndex);

    qDebug()<<QString("%1各数据下标:").arg(strLog)<<iStartIndex<<iUPMaxIndex<<iDownMaxIndex<<iEndIndex;
    double dUPTempDiff = dTempVec.at(iUPMaxIndex) - dTempVec.at(iStartIndex);
    double dUPTimeDiff = pStruct->dTimeVec.at(iUPMaxIndex) - pStruct->dTimeVec.at(iStartIndex);
    double dUPRate = dUPTempDiff / dUPTimeDiff;
    pStruct->strDetails += QString("模组%1升温速率%2;").arg(index + 1).arg(dUPRate);
    bool bUPSuccess = false;
    if(dUPRate >= m_sParamsStruct.dUpRate)
    {
        bUPSuccess = true;
        qDebug()<<QString("%1升温速率%2符合要求").arg(strLog).arg(dUPRate)<<m_sParamsStruct.dUpRate<<dUPTempDiff<<dUPTimeDiff;
    }
    else
    {
        bUPSuccess = false;
        qDebug()<<QString("%1升温速率%2不符合要求").arg(strLog).arg(dUPRate)<<m_sParamsStruct.dUpRate<<dUPTempDiff<<dUPTimeDiff;

        if(0 == index)
            emit CPublicConfig::GetInstance()->SignalSaveFaultCode(803, iMachineID);
        else
            emit CPublicConfig::GetInstance()->SignalSaveFaultCode(805, iMachineID);
    }

    double dDownTempDiff = dTempVec.at(iDownMaxIndex) - dTempVec.at(iEndIndex);
    double dDownTimeDiff = abs(pStruct->dTimeVec.at(iDownMaxIndex) - pStruct->dTimeVec.at(iEndIndex));
    double dDownRate = dDownTempDiff / dDownTimeDiff;
    pStruct->strDetails += QString("模组%1降温速率%2;").arg(index + 1).arg(dDownRate);
    bool bDownSuccess = false;
    if(dDownRate >= m_sParamsStruct.dDownRate)
    {
        bDownSuccess = true;
        qDebug()<<QString("%1降温速率%2符合要求").arg(strLog).arg(dDownRate)<<m_sParamsStruct.dDownRate<<dDownTempDiff<<dDownTimeDiff;
    }
    else
    {
        bDownSuccess = false;
        qDebug()<<QString("%1降温速率%2不符合要求").arg(strLog).arg(dDownRate)<<m_sParamsStruct.dDownRate<<dDownTempDiff<<dDownTimeDiff;

        if(0 == index)
            emit CPublicConfig::GetInstance()->SignalSaveFaultCode(804, iMachineID);
        else
            emit CPublicConfig::GetInstance()->SignalSaveFaultCode(806, iMachineID);
    }

    int iResult = -1;
    if(bUPSuccess && bDownSuccess)
        iResult = 0;
    qDebug()<<QString("%1自检结束,%2").arg(strLog).arg(0 == iResult ? "成功" : "失败");

    if(0 == index)
        pStruct->iResult1 = iResult;
    else
        pStruct->iResult2 = iResult;
}

void CSelfTestTEC::_WriteXlsx(int iMachineID)
{
    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = pStruct->strXlsxPath;
    pXlsxStruct->strTableName = "tec";
    pXlsxStruct->bDrawChart = true;
    pXlsxStruct->strTitleList << "time" << "Module1Temp" << "Module2Temp" << "info";

    QString strResult = "失败";
    if(0 == pStruct->iResult1 && 0 == pStruct->iResult2)
        strResult = "成功";
    strResult = strResult + "  " + pStruct->strDetails;

    int size = pStruct->dTimeVec.size();
    for(int i=0; i<size; i++)
    {
        QVariantList qRowList = {pStruct->dTimeVec.at(i), pStruct->dTemp1Vec.at(i), pStruct->dTemp2Vec.at(i)};
        if(0 == i)
            qRowList << strResult;
        else
            qRowList << QVariant();

        pXlsxStruct->varWriteDataList << qRowList;
    }

    ChartNoteStruct chart;
    chart.iRow = 4;
    chart.iColumn = 5;
    chart.strChartTitle = "tec data";
    chart.strXTitle = "time (s)";
    chart.strYTitle = "temp (℃)";
    chart.strSerialNameList << "Module1Temp" << "Module2Temp";
    chart.strSerialColorList<<HEX_COLOR_B<<HEX_COLOR_G;
    chart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(size + 1);
    chart.strNumDataRange = QString("B2:C%1").arg(size + 1);
    chart.bMajorGridlines = false;
    chart.strMarkSymbolList << "none" << "none";

    pXlsxStruct->chartNoteList << chart;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}
