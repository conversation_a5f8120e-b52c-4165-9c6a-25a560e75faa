#ifndef CSELFTESTPRESSURE_H
#define CSELFTESTPRESSURE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-07-29
  * Description: 气压自检
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CCmdBase.h"
#include "PublicParams.h"

class CSelfTestPressure : public QObject , public CCmdBase
{
    Q_OBJECT
public:
    explicit CSelfTestPressure(QObject *parent = nullptr);
    ~CSelfTestPressure();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

    void StartSelfTest(int iMachineID, QString strXlsxPath);
    void EndSelfTest(int iMachineID, bool bStop);

signals:
    void SignalSelfTestResult(int iMachineID, int iResult);

private:
    void _HandleGasInfo(int iMachineID, const QVariant &qVarData);
    void _ReceiveStart(int iMachineID, int iResult);
    bool _CheckGasRange(int iMachineID, int iRoute);
    void _WriteXlsx(int iMachineID);
    void _CheckTimingExist();

private:
    struct SSelfTestInfoStruct
    {
        SSelfTestInfoStruct()
        {
            Clear();
        }

        void Clear()
        {
            bStart = false;
            iResult = -1;
            strXlsxPath.clear();
            strDetails.clear();
            dTimeVec.clear();
            dP1Vec.clear();
            dP2Vec.clear();
        }

        bool bStart;              //是否启动自检
        int iResult;              //自检结果,两个气嘴,1个传感器
        QString strXlsxPath;      //内部自检结果表
        QString strDetails;       //详情
        QDateTime qBeginTime;     //开始时间
        QVector<double> dTimeVec; //时间
        QVector<double> dP1Vec, dP2Vec; //气压值
    };

private:
    QString m_strTimingName;
    QString m_strTimingData;
    SPressureSelfTestParamsStruct m_sParamsStruct;
    QList<SSelfTestInfoStruct *> m_pInfoStructList;
};

#endif // CSELFTESTPRESSURE_H
