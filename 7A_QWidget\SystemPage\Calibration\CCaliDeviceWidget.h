#ifndef CCALIDEVICEWIDGET_H
#define CCALIDEVICEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-08-05
  * Description: 校准-仪器页
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include "CCaliDevItemWidget.h"
#include "CmdBus/CCmdBase.h"

class CCaliDeviceWidget : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CCaliDeviceWidget(QWidget *parent = nullptr);
    ~CCaliDeviceWidget();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

protected:
    virtual void showEvent(QShowEvent *pEvent);

signals:
    void SignalReturn();

private:
    void _Init_1x8();

private:
    int m_iDevNum;
    int m_iItemNum;
    QPushButton *m_pReturnBtn;
    QLabel *m_pInfoLabel;
    QList<CCaliDevItemWidget *> m_pDevItemList;
};

#endif // CCALIDEVICEWIDGET_H
