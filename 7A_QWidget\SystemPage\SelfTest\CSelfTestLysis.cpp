#include "CSelfTestLysis.h"
#include "CReadWriteXlsxThread.h"

CSelfTestLysis::CSelfTestLysis(QObject *parent) : QObject(parent)
{
    Register2Map(Method_HTST);
    Register2Map(Method_ht_info);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_pInfoStructList.push_back(new SSelfTestInfoStruct());
    }
}

CSelfTestLysis::~CSelfTestLysis()
{
    UnRegister2Map(Method_HTST);
    UnRegister2Map(Method_ht_info);

    for(int i=0; i<m_pInfoStructList.size(); i++)
    {
        SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_pInfoStructList.clear();
}

void CSelfTestLysis::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);

    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(!pStruct->bStart)
        return;

    if(Method_ht_info == iMethodID)
        _HandleHTInfo(iMachineID, qVarData);
}

void CSelfTestLysis::StartSelfTest(int iMachineID, QString strXlsxPath)
{
    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    m_sParamsStruct = CPublicConfig::GetInstance()->GetSelfTestParamsStruct().sLysisStruct;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    pStruct->Clear();
    pStruct->bStart = true;
    pStruct->strXlsxPath = strXlsxPath;

    QVariantList qVarList = {15, m_sParamsStruct.dMaxTemp * 100};
    QString strCmd = GetJsonCmdString(Method_HTST, qVarList);
    qDebug()<<QString("%1#开始热裂解自检:").arg(iMachineID + 1)<<m_sParamsStruct.dMaxTemp
           <<m_sParamsStruct.dUpRate<<strCmd;
    SendJsonCmd(iMachineID, Method_HTST, strCmd);
}

void CSelfTestLysis::EndSelfTest(int iMachineID)
{
    qDebug()<<Q_FUNC_INFO<<iMachineID;
    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(pStruct->bStart)
    {
        QVariantList qVarList = {15};
        QString strCmd = GetJsonCmdString(Method_HTSP, qVarList);
        SendJsonCmd(iMachineID, Method_HTSP, strCmd);
    }

    pStruct->Clear();
}

void CSelfTestLysis::_HandleHTInfo(int iMachineID, const QVariant &qVarData)
{
    QStringList strList = qVarData.toString().split(';');
    if(strList.size() < 4)
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(pStruct->dTimeVec.isEmpty())
    {
        pStruct->qBeginTime = QDateTime::currentDateTime();
        pStruct->dTimeVec.push_back(0);
    }
    else
    {
        double dTime = pStruct->qBeginTime.msecsTo(QDateTime::currentDateTime());
        pStruct->dTimeVec.push_back(dTime / 1000.0);
    }

    for(int i=0; i<4 ;i++)
    {
        QStringList strTempList = strList.at(i).split(',');
        double dTemp = 0;
        if(strTempList.size() >= 2)
            dTemp = strTempList.at(1).toDouble() / 100.0;
        pStruct->iTempMap[i].push_back(dTemp);

        _CheckTempRate(iMachineID, i, dTemp);
    }

    //模块4不检测
    if(!pStruct->bHasCheckVec.at(0) || !pStruct->bHasCheckVec.at(1) || !pStruct->bHasCheckVec.at(2))
        return;

    if(!pStruct->bResultVec.at(0))
        emit CPublicConfig::GetInstance()->SignalSaveFaultCode(807, iMachineID);
    if(!pStruct->bResultVec.at(1))
        emit CPublicConfig::GetInstance()->SignalSaveFaultCode(808, iMachineID);
    if(!pStruct->bResultVec.at(2))
        emit CPublicConfig::GetInstance()->SignalSaveFaultCode(809, iMachineID);

    int iResult = -1;
    if(pStruct->bResultVec.at(0) && pStruct->bResultVec.at(1) && pStruct->bResultVec.at(2))
        iResult = 0;

    pStruct->bStart = false;
    _WriteXlsx(iMachineID);
    qDebug()<<QString("%1#热裂解自检,各模块升温速率:").arg(iMachineID + 1)<<pStruct->dRateVec;
    qDebug()<<QString("%1#热裂解自检结束,%2").arg(iMachineID + 1).arg(0 == iResult ? "成功" : "失败");
    QVariantList qVarList = {15};
    QString strCmd = GetJsonCmdString(Method_HTSP, qVarList);
    SendJsonCmd(iMachineID, Method_HTSP, strCmd);

    emit SignalSelfTestResult(iMachineID, iResult);
}

void CSelfTestLysis::_CheckTempRate(int iMachineID, int index, double dTemp)
{
    //模块4不检测
    if(index >= 3)
        return;

    //没到预定温度
    if(dTemp < m_sParamsStruct.dMaxTemp)
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(pStruct->bHasCheckVec[index]) //已经检测过
        return;
    pStruct->bHasCheckVec[index] = true;

    double dRate = dTemp / pStruct->dTimeVec.last();
    pStruct->strDetails += QString("模组%1升温速率%2;").arg(index + 1).arg(dRate);
    pStruct->dRateVec[index] = dRate;
    pStruct->bResultVec[index] = dRate >= m_sParamsStruct.dUpRate;
}

void CSelfTestLysis::_WriteXlsx(int iMachineID)
{
    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = pStruct->strXlsxPath;
    pXlsxStruct->strTableName = "lysis";
    pXlsxStruct->bDrawChart = true;
    pXlsxStruct->strTitleList << "time" << "Module1Temp" << "Module2Temp" << "Module3Temp" << "Module4Temp" << "info";

    QString strResult = "失败";
    if(pStruct->bResultVec.at(0) && pStruct->bResultVec.at(1) && pStruct->bResultVec.at(2))
        strResult = "成功";
    strResult = strResult + "  " + pStruct->strDetails;

    int size = pStruct->dTimeVec.size();
    for(int i=0; i<size; i++)
    {
        QVariantList qRowList = {pStruct->dTimeVec.at(i), pStruct->iTempMap[0].at(i), pStruct->iTempMap[1].at(i),
                                 pStruct->iTempMap[2].at(i), pStruct->iTempMap[3].at(i)};
        if(0 == i)
            qRowList << strResult;
        else
            qRowList << QVariant();

        pXlsxStruct->varWriteDataList << qRowList;
    }

    ChartNoteStruct chart;
    chart.iRow = 4;
    chart.iColumn = 7;
    chart.strChartTitle = "lysis data";
    chart.strXTitle = "time (s)";
    chart.strYTitle = "temp (℃)";
    chart.strSerialNameList << "Module1Temp" << "Module2Temp" << "Module3Temp" << "Module4Temp";
    chart.strSerialColorList<<HEX_COLOR_B<<HEX_COLOR_G<<HEX_COLOR_Y<<HEX_COLOR_R;
    chart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(size + 1);
    chart.strNumDataRange = QString("B2:E%1").arg(size + 1);
    chart.bMajorGridlines = false;
    chart.strMarkSymbolList << "none" << "none" << "none" << "none";

    pXlsxStruct->chartNoteList << chart;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}
