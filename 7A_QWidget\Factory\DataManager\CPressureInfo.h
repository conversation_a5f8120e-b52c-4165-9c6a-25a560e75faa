#ifndef CPRESSUREINFO_H
#define CPRESSUREINFO_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-05-06
  * Description:
  * -------------------------------------------------------------------------
  * History: 20250526 hxr 不再new多个表格，改为多台机器共用1个表格
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CPressureOneCurve.h"
#include "CBusyProgressBar.h"

class CPressureInfo : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CPressureInfo(QWidget *parent = nullptr);
    ~CPressureInfo();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotClearData(int iMachineID);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotMachineChanged(int iMachineID);
    void _SlotListBtn();
    void _SlotCancelCheck();
    void _SlotSetXYRange(const QStringList &strList);
    void _SlotPlotClicked(QCPAbstractPlottable *pPlottable, int iDataIndex, QMouseEvent *pEvent);

private:
    void _ReceiveData(int iMachineID, QVariant qVarData);
    void _ClearData(int iMachineID);
    void _ExportData(int iMachineID);
    void _WriteData2Xlsx(int iMachineID);

    void _RunCheckFun();
    void _TestEnd(int iMachineID);
    void _StartTimingTest(int iMachineID, QString strTecName, QString strTimingName);
    bool _HandleCheckData(int iMachineID);

private:
    void _InitCustomPlot();
    void _AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName);
    void _InitWidget();
    void _InitLayout();

private:
    struct SGasInfoStruct
    {
        SGasInfoStruct()
        {
            Clear();
        }

        void Clear()
        {
            bReplot = false;
            strCPItemText = "pressure1:\npressure2:\np1-p2:";
            strXYRangeList.clear();
            strXYRangeList << "0" << "100" << "0" << "40";
            dTimeVec.clear();
            dP1Vec.clear();
            dP2Vec.clear();
        }

        bool bReplot;
        QString strCPItemText;
        QStringList strXYRangeList;
        QDateTime qBeginDateTime;
        QVector<double> dTimeVec, dP1Vec, dP2Vec;
    };

private:
    bool m_bShow;
    CLabelComboBox *m_pMachineComboBox;
    QList<QPushButton *> m_pBtnList;
    CBusyProgressBar *m_pCBusyProgressBar;
    QCustomPlot *m_pCustomPlot;
    QCPItemText *m_pCPItemText;
    CSetChartXYRange *m_pCSetChartXYRange;
    QList<SGasInfoStruct *> m_sGasInfoList;

    //气密性工装变量
    bool m_bRunCheck;
    int m_iCheckStep;
    QString m_strXlsxName;
    QVector<double> m_dTimeVec1, m_dDataVec1, m_dDataVec2;
    QVector<double> m_dTimeVec2, m_dDataVec3, m_dDataVec4;
};

#endif // CPRESSUREINFO_H
