#ifndef CRANGELINEEDIT_H
#define CRANGELINEEDIT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-07-30
  * Description: 范围输入
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include "CLineEdit.h"

class CRangeLineEdit : public QWidget
{
    Q_OBJECT
public:
    explicit CRangeLineEdit(QString strName, QWidget *parent = nullptr);
    ~CRangeLineEdit();

    void ResetLabelSize(int iWidth, int iHeight);

    double GetMinValue() const;
    double GetMaxValue() const;

    void GetRangeValue(double &dMinValue, double &dMaxValue) const;
    void SetRangeValue(const double &dMinValue,const double &dMaxValue);
    void Clear();

private:
    QLabel *m_pNameLabel;
    CLineEdit *m_pMinLineEdit;
    QLabel *m_pLineLabel;
    CLineEdit *m_pMaxLineEdit;
};

#endif // CRANGELINEEDIT_H
