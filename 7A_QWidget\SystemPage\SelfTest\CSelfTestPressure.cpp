#include "CSelfTestPressure.h"

#include "CRunTest.h"
#include "CTimingTecDB.h"
#include "CReadWriteXlsxThread.h"

CSelfTestPressure::CSelfTestPressure(QObject *parent) : QObject(parent)
{
    Register2Map(Method_pressure_info);
    Register2Map(Method_start);

    _CheckTimingExist();

    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_pInfoStructList.push_back(new SSelfTestInfoStruct());
    }
}

CSelfTestPressure::~CSelfTestPressure()
{
    UnRegister2Map(Method_pressure_info);
    UnRegister2Map(Method_start);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_pInfoStructList.clear();
}

void CSelfTestPressure::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(!pStruct->bStart)
        return;

    if(Method_pressure_info == iMethodID)
        _HandleGasInfo(iMachineID, qVarData);
    else if(Method_start == iMethodID)
        _ReceiveStart(iMachineID, iResult);
}

void CSelfTestPressure::StartSelfTest(int iMachineID, QString strXlsxPath)
{
    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    m_sParamsStruct = CPublicConfig::GetInstance()->GetSelfTestParamsStruct().sPressureStruct;
    qDebug()<<Q_FUNC_INFO<<"自检参数:"<<m_sParamsStruct.gas1.dAddBeginTime<<m_sParamsStruct.gas1.dAddEndTime
           <<m_sParamsStruct.gas1.dAddMinValue<<m_sParamsStruct.gas1.dAddMaxValue
          <<m_sParamsStruct.gas1.dKeepBeginTime<<m_sParamsStruct.gas1.dKeepEndTime
         <<m_sParamsStruct.gas1.dKeepMinValue<<m_sParamsStruct.gas1.dKeepMaxValue
        <<m_sParamsStruct.gas1.dSubBeginTime<<m_sParamsStruct.gas1.dSubEndTime<<m_sParamsStruct.gas1.dSubMinValue
       <<m_sParamsStruct.gas2.dAddBeginTime<<m_sParamsStruct.gas2.dAddEndTime
      <<m_sParamsStruct.gas2.dAddMinValue<<m_sParamsStruct.gas2.dAddMaxValue
     <<m_sParamsStruct.gas2.dKeepBeginTime<<m_sParamsStruct.gas2.dKeepEndTime
    <<m_sParamsStruct.gas2.dKeepMinValue<<m_sParamsStruct.gas2.dKeepMaxValue
    <<m_sParamsStruct.gas2.dSubBeginTime<<m_sParamsStruct.gas2.dSubEndTime<<m_sParamsStruct.gas2.dSubMinValue;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    pStruct->Clear();
    pStruct->bStart = true;
    pStruct->strXlsxPath = strXlsxPath;

    QString strTimingData = CTimingTecDB::GetInstance().GetTimingContent(m_strTimingName);
    if(strTimingData.isEmpty())
        strTimingData = m_strTimingData;

    QDateTime dateTime = QDateTime::currentDateTime();
    QString strCurrentTime = dateTime.toString("yyyyMMddhhmmss");
    QString strCardID = "C" + strCurrentTime;
    QString strSampleID = "S" + strCurrentTime;
    QString strProject = "2019-nCoV/FluA/FluB/RSV";

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);
    sRunInfo.bFactroyTest = false;
    sRunInfo.iRunTimes = 1;
    sRunInfo.strTecName = "";
    sRunInfo.strTimingName = m_strTimingName;
    sRunInfo.strTimingData = strTimingData;
    sRunInfo.sCardInfo.strCardID = strCardID;
    sRunInfo.sCardInfo.strProject = strProject;
    sRunInfo.sSampleInfo.strSampleID = strSampleID;
    sRunInfo.sSampleInfo.strQCTestModel = "T";
    sRunInfo.sSampleInfo.strOperator = CPublicConfig::GetInstance()->GetLoginUser();
    qDebug()<<QString("%1#开始气压自检时序测试,时序:%2,%3").arg(iMachineID + 1).arg(m_strTimingName).arg(strTimingData);

    CRunTest::GetInstance()->StartTest(iMachineID);
}

void CSelfTestPressure::EndSelfTest(int iMachineID, bool bStop)
{
    qDebug()<<Q_FUNC_INFO<<iMachineID;
    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(pStruct->bStart && bStop)
    {
        QString strCmd = GetJsonCmdString(Method_stop);
        SendJsonCmd(iMachineID, Method_stop, strCmd);
    }

    pStruct->Clear();
}

void CSelfTestPressure::_HandleGasInfo(int iMachineID, const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.isEmpty())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);

    //除以10000
    double dP1 = qVarList.at(0).toDouble() / 10000.0;
    dP1 *= 6.89;
    pStruct->dP1Vec.push_back(dP1);

    double dP2 = 0;
    if(qVarList.size() >= 2)
    {
        dP2 = qVarList.at(1).toDouble() / 10000.0;
        dP2 *= 6.89;
    }
    pStruct->dP2Vec.push_back(dP2);

    //秒
    double dTime = 0;
    if(pStruct->dTimeVec.isEmpty())
        pStruct->qBeginTime = QDateTime::currentDateTime();
    else
        dTime = pStruct->qBeginTime.msecsTo(QDateTime::currentDateTime()) / 1000.0;
    pStruct->dTimeVec.push_back(dTime);
}

void CSelfTestPressure::_ReceiveStart(int iMachineID, int iResult)
{
    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    pStruct->bStart = false;

    if(0 != iResult)
    {
        pStruct->iResult = -1;
        pStruct->strDetails = "时序运行失败";
    }
    else
    {
        if(!_CheckGasRange(iMachineID, 1) || !_CheckGasRange(iMachineID, 2))
        {
            pStruct->iResult = -1;
        }
        else
        {
            pStruct->iResult = 0;
        }
    }

    if(0 != pStruct->iResult)
        emit CPublicConfig::GetInstance()->SignalSaveFaultCode(811, iMachineID);

    _WriteXlsx(iMachineID);
    emit SignalSelfTestResult(iMachineID, pStruct->iResult);
}

bool CSelfTestPressure::_CheckGasRange(int iMachineID, int iRoute)
{
    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    QString strLog = QString("%1#气压自检气路%2").arg(iMachineID + 1).arg(iRoute);
    qDebug()<<strLog<<"数据:"<<pStruct->dTimeVec<<pStruct->dP1Vec;

    SGasSelfTestParmsStruct gas;
    if(1 == iRoute)
        gas = m_sParamsStruct.gas1;
    else
        gas = m_sParamsStruct.gas2;

    //加压检测
    int iAddIndex1 = -1, iAddIndex2 = -1;
    for(int i=0; i<pStruct->dTimeVec.size(); i++)
    {
        double dSec = pStruct->dTimeVec.at(i);
        if(iAddIndex1 < 0 && dSec >= gas.dAddBeginTime)
        {
            iAddIndex1 = i;
        }
        if(dSec > gas.dAddEndTime)
        {
            iAddIndex2 = i;
            break;
        }
    }
    if(-1 == iAddIndex1 || -1 == iAddIndex2) //防止数据异常
    {
        return false;
    }
    double dMaxValue = pStruct->dP1Vec.at(iAddIndex1);
    for(int i=iAddIndex1; i<iAddIndex2; i++)
    {
        dMaxValue = qMax(dMaxValue, pStruct->dP1Vec.at(i));
    }
    if(dMaxValue >= gas.dAddMinValue && dMaxValue <= gas.dAddMaxValue)
    {
        qDebug()<<strLog<<QString("加压最大值%1符合要求").arg(dMaxValue);
    }
    else
    {
        pStruct->strDetails = QString("加压最大值%1不符合要求").arg(dMaxValue);
        qDebug()<<strLog<<pStruct->strDetails;
        return false;
    }

    //平衡检测
    int iKeepIndex1 = -1, iKeepIndex2 = -1;
    for(int i=0; i<pStruct->dTimeVec.size(); i++)
    {
        double dSec = pStruct->dTimeVec.at(i);
        if(iKeepIndex1 < 0 && dSec >= gas.dKeepBeginTime)
        {
            iKeepIndex1 = i;
        }
        if(dSec > gas.dKeepEndTime)
        {
            iKeepIndex2 = i;
            break;
        }
    }
    if(-1 == iKeepIndex1 || -1 == iKeepIndex2) //防止数据异常
    {
        return false;
    }
    for(int i=iKeepIndex1; i<iKeepIndex2; i++)
    {
        double dValue = pStruct->dP1Vec.at(i);
        if(dValue < gas.dKeepMinValue || dValue > gas.dKeepMaxValue)
        {
            pStruct->strDetails = QString("平衡检测不符合要求,%1超出范围").arg(dValue);
            qDebug()<<strLog<<pStruct->strDetails;
            return false;
        }
    }

    //泄压检测
    int iSubIndex1 = -1, iSubIndex2 = -1;
    for(int i=0; i<pStruct->dTimeVec.size(); i++)
    {
        double dSec = pStruct->dTimeVec.at(i);
        if(iSubIndex1 < 0 && dSec >= gas.dSubBeginTime)
        {
            iSubIndex1 = i;
        }
        if(dSec > gas.dSubEndTime)
        {
            iSubIndex2 = i;
            break;
        }
    }
    if(-1 == iSubIndex1 || -1 == iSubIndex2) //防止数据异常
    {
        return false;
    }
    double dMinValue = pStruct->dP1Vec.at(iSubIndex1);
    for(int i=iSubIndex1; i<iSubIndex2; i++)
    {
        dMinValue = qMin(dMinValue, pStruct->dP1Vec.at(i));
    }
    if(dMinValue <= gas.dSubMinValue)
    {
        pStruct->strDetails = "泄压检测符合要求";
    }
    else
    {
        pStruct->strDetails = "泄压不符合要求,最小值:" + QString::number(dMinValue);
        return false;
    }
    return true;
}

void CSelfTestPressure::_WriteXlsx(int iMachineID)
{
    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = pStruct->strXlsxPath;
    pXlsxStruct->strTableName = "pressure";
    pXlsxStruct->bDrawChart = true;
    pXlsxStruct->strTitleList << "time" << "pressure1" << "pressure2";

    QString strResult = "失败";
    if(0 == pStruct->iResult)
        strResult = "成功";
    pStruct->strDetails = strResult + "  " + pStruct->strDetails;

    int size = pStruct->dTimeVec.size();
    for(int i=0; i<size; i++)
    {
        QVariantList qRowList = {pStruct->dTimeVec.at(i), pStruct->dP1Vec.at(i), pStruct->dP2Vec.at(i)};
        if(0 == i)
            qRowList << pStruct->strDetails;
        else
            qRowList << QVariant();

        pXlsxStruct->varWriteDataList << qRowList;
    }

    ChartNoteStruct chart;
    chart.iRow = 4;
    chart.iColumn = 5;
    chart.strChartTitle = "pressure data";
    chart.strXTitle = "time (s)";
    chart.strYTitle = "pressure (kpa)";
    chart.strSerialNameList << "pressure1" << "pressure2";
    chart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(size + 1);
    chart.strNumDataRange = QString("B2:C%1").arg(size + 1);
    chart.bMajorGridlines = false;
    chart.strMarkSymbolList << "none" << "none";

    pXlsxStruct->chartNoteList << chart;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CSelfTestPressure::_CheckTimingExist()
{
    m_strTimingName = "pressureSelfTest";
    m_strTimingData = "258;43;50;52;10000,313;10000,311;53;10000,292;48;10000,310,9000,10000;"
                      "1793,50;10000,291;1793,5;10000,292;49;10000,310,6000,10000;1793,50;10000,291;1793,5;44";

    if(!CTimingTecDB::GetInstance().IsTimingExist(m_strTimingName))
    {
        qDebug()<<"添加气压自检时序:"<<m_strTimingName<<m_strTimingData;
        CTimingTecDB::GetInstance().AddOneTiming(m_strTimingName, "", m_strTimingData);
    }
}
