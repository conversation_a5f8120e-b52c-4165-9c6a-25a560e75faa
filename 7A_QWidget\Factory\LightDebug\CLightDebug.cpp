#include "CLightDebug.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QDebug>
#include <QTime>
#include <QDateTime>
#include <QDir>
#include <QFileInfo>
#include <thread>

#include "CLightSingleCmd.h"
#include "CLightCurve.h"
#include "CLightCalibrate.h"
#include "CLightMDT.h"
#include "COpticalCalibration.h"
#include "CFluorescenceInterference.h"
#include "CPerformanceTest.h"
#include "CNoiseTest.h"
#include "CMessageBox.h"
#include "PublicFunction.h"

CLightDebug::CLightDebug(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    connect(this, &CLightDebug::SignalExportError, this, &CLightDebug::_SlotExportError);
    connect(this, &CLightDebug::SignalExportEnd, this, &CLightDebug::_SlotExportEnd);
}

CLightDebug::~CLightDebug()
{

}

void CLightDebug::_SlotTitleChanged(int index)
{
    m_pStackedWidgetFunction->setCurrentIndex(index);
}

void CLightDebug::_SlotCreateFlTestBtn()
{
    QString strMachineSN;
    strMachineSN = m_pSNLineEdit->GetLineEditText();
    if(strMachineSN.isEmpty())
    {
        ShowWarning(nullptr, gk_strTipsText, tr("请输入SN"));
        return;
    }
    else
    {
        m_pSNLabel->SetValueLabelText(strMachineSN);
        QString strDateTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
        QString strSNXlsxName = QString("FlToolData_%1_%2.xlsx").arg(strMachineSN).arg(strDateTime);
        CPublicConfig::GetInstance()->SetLightSNXlsxName(strSNXlsxName);
        m_pPerformanceTest->ClearAllData();
        m_pOpticalCalibration->ClearAllData();
        m_pFluorescenceInterference->ClearAllData();
        
        // 设置荧光类型到性能测试页面
        int iFluorescenceType = m_pFluorescenceTypeComboBox->GetCurrentIndex();
        m_pPerformanceTest->SetFluorescenceType(iFluorescenceType);
        m_pFluorescenceInterference->SetFluorescenceType(iFluorescenceType);
        
        m_pStackedWidget->setCurrentIndex(1);
    }
}

void CLightDebug::_SlotReturnCreateBtn()
{
    m_pStackedWidget->setCurrentIndex(0);
}

void CLightDebug::_SlotFluorescenceTypeChanged(int iTypeIndex)
{
    // 荧光类型变化时的处理逻辑
    // 当前主要影响性能测试页面的显示
}

void CLightDebug::_SlotShowDateWidget()
{
    CLabelDate *pCLabelDate = dynamic_cast<CLabelDate *>(sender());
    if(nullptr == pCLabelDate)
        return;
    m_eDateType = (EnumDateType)pCLabelDate->property("DateType").toInt();
    m_pDateWidget->SetDate(pCLabelDate->GetDateString());
    
    // 将日期选择对话框移到最前面显示
    m_pDateWidget->raise();
    m_pDateWidget->show();
}

void CLightDebug::_SlotConfirmDate(const QString &strDate)
{
    switch (m_eDateType)
    {
    case eStartDate:
        m_pBeginDate->SetDateString(strDate);
        break;
    case eEndDate:
        m_pEndDate->SetDateString(strDate);
        break;
    default:
        break;
    }
}

void CLightDebug::_SlotExportError()
{
    ShowError(this, gk_strTipsText, tr("Xlsx导出失败，请重试"));
}

void CLightDebug::_SlotExportEnd()
{
    ShowSuccess(this, gk_strTipsText, tr("Xlsx导出完成"));
}
void CLightDebug::_SlotExportExcelBtn()
{
    // 检查是否有U盘
    if(!UDiskExist(this))
        return;

    QString strBeginDate = m_pBeginDate->GetDateString();
    QString strEndDate = m_pEndDate->GetDateString();
    std::thread exportThread(&CLightDebug::_Thread2ExportExcel, this, strBeginDate, strEndDate);
    exportThread.detach();
}

void CLightDebug::_Thread2ExportExcel(const QString &strBeginDate, const QString &strEndDate)
{
    qDebug()<<"导出光学Xlsx:"<<strBeginDate<<strEndDate;
    
    QString strExportDir = GetUDiskDir() + "7C_LightDebug_" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    CreateDir(strExportDir);
    
    // 获取当前应用程序目录下的光学测试相关文件
    QString strAppDir = CPublicConfig::GetInstance()->GetXlsxDir();
    QDir appDir(strAppDir);
    
    // 使用日期过滤搜索结果文件
    QStringList strExportResultList = _GetMatchDateFilesFromDir(appDir, strBeginDate, strEndDate);
    
    int iExportedCount = 0;
    
    // 再导出符合日期范围的结果文件
    for(const QString &filePath : strExportResultList)
    {
        bool bCopy = CopyQFileDir(filePath, QDir(strExportDir));
        if(bCopy)
        {
            iExportedCount++;
            QFileInfo fileInfo(filePath);
            qDebug() << "导出结果文件:" << fileInfo.fileName();
        }
    }
    
    // 同步文件系统
    System("sync");
    ExportEndUmountUSB();
    
    // 在主线程中显示结果
    if(iExportedCount > 0)
    {
        qDebug() << QString("Excel文件导出完成，共导出%1个文件").arg(iExportedCount);
        emit SignalExportEnd();
    }
    else
    {
        qDebug() << "未找到可导出的Excel文件";
        emit SignalExportError();
    }
}

QStringList CLightDebug::_GetMatchDateFilesFromDir(const QDir &qDir, const QString &strBeginDate, const QString &strEndDate)
{
    QStringList strFileList;
    QDate qBeginDate = QDate::fromString(strBeginDate, "yyyy-MM-dd");
    QDate qEndDate = QDate::fromString(strEndDate, "yyyy-MM-dd");
    if(qBeginDate > qEndDate)
    {
        qBeginDate = QDate::fromString(strEndDate, "yyyy-MM-dd");
        qEndDate = QDate::fromString(strBeginDate, "yyyy-MM-dd");
    }

    // 搜索FlToolData_*.xlsx格式的文件
    QStringList nameFilters;
    nameFilters << "FlToolData_*.xlsx";
    QFileInfoList fileList = qDir.entryInfoList(nameFilters, QDir::Files);
    
    for(int i=0; i<fileList.size(); i++)
    {
        QFileInfo fileInfo = fileList.at(i);
        QStringList strNameList = fileInfo.baseName().split("_");
        if(strNameList.size() < 2)
            continue;
            
        QDate qFileDate;
        // FlToolData_SN_yyyyMMddhhmmss.xlsx格式，提取日期部分
        if(strNameList.size() >= 3)
        {
            QString strDateTime = strNameList.at(2);
            if(strDateTime.length() >= 8)
            {
                qFileDate = QDate::fromString(strDateTime.mid(0, 8), "yyyyMMdd");
            }
        }
        
        // 如果无法从文件名解析日期，使用文件修改时间
        if(!qFileDate.isValid())
        {
            qFileDate = fileInfo.lastModified().date();
        }

        if(qFileDate.isValid() && qFileDate >= qBeginDate && qFileDate <= qEndDate)
        {
            strFileList.push_back(fileInfo.absoluteFilePath());
        }
    }

    return strFileList;
}

void CLightDebug::_InitWidget()
{
    m_pWidgetCreate = new QWidget;
    
    // 创建荧光类型下拉框
    QStringList strFluorescenceTypeList = {tr("荧光片"), tr("染料")};
    m_pFluorescenceTypeComboBox = new CLabelComboBox(tr("荧光类型:"), strFluorescenceTypeList);
    m_pFluorescenceTypeComboBox->SetComboBoxFixedSize(150, 50);
    m_pFluorescenceTypeComboBox->SetCurrentIndex(1);
    connect(m_pFluorescenceTypeComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotFluorescenceTypeChanged(int)));
    
    m_pSNLineEdit = new CLabelLineEdit(tr("输入SN:"));
    m_pSNLineEdit->SetLineEditFixedSize(150, 50);

    m_pCreateFlTestBtn = new QPushButton(tr("创建光学测试"));
    m_pCreateFlTestBtn->setFixedSize(150, 50);
    connect(m_pCreateFlTestBtn, &QPushButton::clicked, this, &CLightDebug::_SlotCreateFlTestBtn);

    QHBoxLayout *pCreateHLayout = new QHBoxLayout;
    pCreateHLayout->setMargin(0);
    pCreateHLayout->addStretch(1);
    pCreateHLayout->addWidget(m_pFluorescenceTypeComboBox);
    pCreateHLayout->addSpacing(20);
    pCreateHLayout->addWidget(m_pSNLineEdit);
    pCreateHLayout->addSpacing(20);
    pCreateHLayout->addWidget(m_pCreateFlTestBtn);
    pCreateHLayout->addStretch(1);
    QVBoxLayout *pCreateVLayout = new QVBoxLayout;
    pCreateVLayout->setMargin(0);
    pCreateVLayout->addStretch(1);
    pCreateVLayout->addLayout(pCreateHLayout);
    pCreateVLayout->addStretch(1);
    m_pWidgetCreate->setLayout(pCreateVLayout);

    m_pWidgetFunction = new QWidget;

    //创建顶部SN号与返回按钮
    m_pSNLabel = new CLabelLabel(tr("SN:"), tr(""));
    m_pSNLabel->SetValueLabelFixedSize(150, 50);
    m_pReturnCreateBtn = new QPushButton(tr("重新创建测试"));
    m_pReturnCreateBtn->setFixedSize(150, 50);
    connect(m_pReturnCreateBtn, &QPushButton::clicked, this, &CLightDebug::_SlotReturnCreateBtn);
    
    // 创建日期选择控件
    QString strDate = QDateTime::currentDateTime().toString("yyyy-MM-dd");
    m_pBeginDate = new CLabelDate(tr("开始日期:"), strDate);
    m_pBeginDate->SetDateFixedSize(120, 50);
    m_pBeginDate->setProperty("DateType", eStartDate);
    connect(m_pBeginDate, &CLabelDate::SignalPressEvent, this, &CLightDebug::_SlotShowDateWidget);

    m_pEndDate = new CLabelDate(tr("结束日期:"), strDate);
    m_pEndDate->SetDateFixedSize(120, 50);
    m_pEndDate->setProperty("DateType", eEndDate);
    connect(m_pEndDate, &CLabelDate::SignalPressEvent, this, &CLightDebug::_SlotShowDateWidget);
    
    m_pExportExcelBtn = new QPushButton(tr("Xlsx导出"));
    m_pExportExcelBtn->setFixedSize(120, 50);
    connect(m_pExportExcelBtn, &QPushButton::clicked, this, &CLightDebug::_SlotExportExcelBtn);

    // 创建日期选择对话框
    m_pDateWidget = new CDateTime(this);
    connect(m_pDateWidget, &CDateTime::SignalConfirmDate, this, &CLightDebug::_SlotConfirmDate);
    m_pDateWidget->setVisible(false);

    QHBoxLayout *pSNHLayout = new QHBoxLayout;
    pSNHLayout->setMargin(0);
    pSNHLayout->addSpacing(30);
    pSNHLayout->addWidget(m_pSNLabel);
    pSNHLayout->addSpacing(30);
    pSNHLayout->addWidget(m_pReturnCreateBtn);
    pSNHLayout->addSpacing(20);
    pSNHLayout->addWidget(m_pBeginDate);
    pSNHLayout->addSpacing(10);
    pSNHLayout->addWidget(m_pEndDate);
    pSNHLayout->addSpacing(10);
    pSNHLayout->addWidget(m_pExportExcelBtn);
    pSNHLayout->addStretch(1);

    QStringList strList = {tr("单指令"), tr("曲线"), tr("校准"), tr("MDT"), tr("光学校准"), tr("荧光干扰"), tr("性能测试"), tr("底噪测试")};
    m_pHBtnTitle = new CHBtnTitleWidget(strList);
    m_pHBtnTitle->SetTitleIndex(0);
    connect(m_pHBtnTitle, &CHBtnTitleWidget::SignalTitleChanged, this, &CLightDebug::_SlotTitleChanged);

    // 创建原有页面
    m_pCLightSingleCmd = new CLightSingleCmd;
    m_pCLightCurve = new CLightCurve;
    m_pCLightCalibrate = new CLightCalibrate;
    m_pCLightMDT = new CLightMDT;
    
    // 创建新增页面
    m_pOpticalCalibration = new COpticalCalibration;
    m_pFluorescenceInterference = new CFluorescenceInterference;
    m_pPerformanceTest = new CPerformanceTest;
    m_pNoiseTest = new CNoiseTest;

    // 连接两个页面的采光测试信号，实现同步计算
    connect(m_pFluorescenceInterference, &CFluorescenceInterference::sigLightTestStarted,
            m_pPerformanceTest, &CPerformanceTest::startCalculation);
    connect(m_pPerformanceTest, &CPerformanceTest::sigLightTestStarted,
            m_pFluorescenceInterference, &CFluorescenceInterference::startCalculation);

    m_pStackedWidgetFunction = new QStackedWidget;
    // 按照标题顺序添加页面
    m_pStackedWidgetFunction->addWidget(m_pCLightSingleCmd);        // 单指令
    m_pStackedWidgetFunction->addWidget(m_pCLightCurve);           // 曲线
    m_pStackedWidgetFunction->addWidget(m_pCLightCalibrate);       // 校准
    m_pStackedWidgetFunction->addWidget(m_pCLightMDT);             // MDT
    m_pStackedWidgetFunction->addWidget(m_pOpticalCalibration);    // 光学校准
    m_pStackedWidgetFunction->addWidget(m_pFluorescenceInterference); // 荧光干扰
    m_pStackedWidgetFunction->addWidget(m_pPerformanceTest);       // 性能测试
    m_pStackedWidgetFunction->addWidget(m_pNoiseTest);             // 底噪测试

    QVBoxLayout *pFunVLayout = new QVBoxLayout;
    pFunVLayout->setMargin(0);
    pFunVLayout->addSpacing(10);
    pFunVLayout->addLayout(pSNHLayout);
    pFunVLayout->addSpacing(10);
    pFunVLayout->addWidget(m_pHBtnTitle);
    pFunVLayout->addSpacing(20);
    pFunVLayout->addWidget(m_pStackedWidgetFunction);
    m_pWidgetFunction->setLayout(pFunVLayout);

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->addWidget(m_pWidgetCreate);
    m_pStackedWidget->addWidget(m_pWidgetFunction);
    m_pStackedWidget->setCurrentIndex(0);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pStackedWidget);

    this->setLayout(pLayout);
}
