#ifndef CRUNTEST_H
#define CRUNTEST_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-15
  * Description: 业务类
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include "PublicParams.h"
#include "CmdBus/CCmdBase.h"

class CRunTest : public QObject , public CCmdBase
{
    Q_OBJECT
public:
    static CRunTest *GetInstance();
    ~CRunTest();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

public:
    void SaveSimutePdf(); //生成测试的pdf,模拟数据

public:
    SRunningInfoStruct &GetRunInfoStruct(int iMachineID);

    void StartTest(int iMachineID);

    void AddPngPath(int iMachineID, QStringList strPathList);
    void AddJsonObj(int iMachineID, QString strName, QJsonObject qJsonObj);
    void UpdatePrintInfo(SPrintInfoStruct sSPrintInfoStruct);
    void WritePdf(int iMachineID, const QString& strPDFName,  const SRunningInfoStruct &sRunInfo,const QStringList& strImagePathList);
signals:
    void SignalAgingLeftTimes(int iMachineID, int iLeftTimes);
    void SignalUpdateItemStatus(int iMachineID, DeviceStatus eStatus);
    void SignalUpdateItemCalcResult(int iMachineID);

private slots:
    void _SlotCalculateUpdateResult(int iMachineID,int intiHistoryID,int iStatus);
    void _SlotDelayAgingTimerout();

private:
    CRunTest();

    void _ParseSendTecEndCmd(int iMachineID, int iResult);
    void _ParseTimingFileCmd(int iMachineID, int iResult, const QVariant &qVarData);
    void _ParseStartCmd(int iMachineID, int iResult);
    void _ParseStopCmd(int iMachineID, int iResult);
    void _ContinueAgingTest(int iMachineID);
    void _SendTimingData(int iMachineID);
    QString _GetRealSendTimingData(const QString &strTimingData); //真实下发的时序数据,需要对原数据做些改动再下发

    void _CalcEnd(int iMachineID,int iStatus);
    void _WriteCalcInfo2Xlsx(int iMachineID);
    void _WriteOther2Xlsx(int iMachineID);

private:
    static CRunTest *m_spInstance;

    QList<QTimer *> m_pDelayAgingTimerList;
    QList<SRunningInfoStruct> m_sRunInfoList;
    QList<QStringList> m_strPngPathList;
    QList<QJsonObject> m_qRootJsonObjList;

    SPrintInfoStruct m_sSPrintInfoStruct;
};

#endif // CRUNTEST_H
