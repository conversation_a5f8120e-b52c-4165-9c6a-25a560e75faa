#ifndef CSELFTESTDEVICEWIDGET_H
#define CSELFTESTDEVICEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-05-23
  * Description: 自检-仪器
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include "CSelfTestTEC.h"
#include "CSelfTestMotor.h"
#include "CSelfTestLysis.h"
#include "CSelfTestPressure.h"
#include "CSelfTestDevItemWidget.h"

class CSelfTestDeviceWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CSelfTestDeviceWidget(QWidget *parent = nullptr);
    ~CSelfTestDeviceWidget();

protected:
    virtual void showEvent(QShowEvent *pEvent);

signals:
    void SignalReturn();

private slots:
    void _SlotStartSelfTest(int iMachineID, QString strXlsxPath);
    void _SlotEndSelfTest(int iMachineID, bool bStop);

    void _SlotTecSelfTestEnd(int iMachineID, int iResult);
    void _SlotLysisSelfTestEnd(int iMachineID, int iResult);
    void _SlotMotorSelfTestEnd(int iMachineID, int iResult);
    void _SlotPressureSelfTestEnd(int iMachineID, int iResult);

private:
    void _Init_1x8();

private:
    struct SSelfTestInfoStruct
    {
        SSelfTestInfoStruct()
        {
            Clear();
        }

        void Clear()
        {
            iTecResult = -1;
            iLysisResult = -1;
            iMotorResult = -1;
            iPressureResult = -1;
            strXlsxPath.clear();
        }

        int iTecResult;
        int iLysisResult;
        int iMotorResult;
        int iPressureResult;
        QString strXlsxPath;
    };

private:
    int m_iDevNum;
    int m_iItemNum;
    QPushButton *m_pReturnBtn;
    QList<CSelfTestDevItemWidget *> m_pDevItemList;

    CSelfTestTEC *m_pCSelfTestTEC;
    CSelfTestLysis *m_pCSelfTestLysis;
    CSelfTestMotor *m_pCSelfTestMotor;
    CSelfTestPressure *m_pCSelfTestPressure;
    QList<SSelfTestInfoStruct *> m_pInfoStructList;
};

#endif // CSELFTESTDEVICEWIDGET_H
