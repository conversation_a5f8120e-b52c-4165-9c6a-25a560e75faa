#ifndef CTIMINGDB_H
#define CTIMINGDB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-04-09
  * Description: 时序数据库, 时序, tec
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CSqliteDBBase.h"

class CTimingTecDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CTimingTecDB &GetInstance();
    virtual ~CTimingTecDB();

    // tec
    bool AddOneTec(const QString &strTecName, const QString &strTecContent);
    bool DeleteOneTec(const QString &strTecName);
    QStringList GetTecNameList();
    QString GetTecContent(const QString &strTecName);
    bool SaveTecList(const QList<QStringList> &strTecList);
    bool IsTecExist(const QString &strTecName);

    // timing
    bool AddOneTiming(const QString &strTimingName, const QString &strTecName, const QString &strTimingContent);
    bool DeleteOneTiming(const QString &strTimingName);
    QStringList GetTimingNameList();    
    QString GetTimingContent(const QString &strTimingName);
    bool SaveTimingList(const QList<QStringList> &strTimingList);
    bool IsTimingExist(const QString &strTimingName);

    QList<QStringList> GetTimingTecNameList();

    QStringList GetProjectRunTimingTec(const QString &strProjectName);

private:
    CTimingTecDB();

    void _InitTecTable();
    void _InitTimingTable();
    void _InitItemTable();

    Q_DISABLE_COPY(CTimingTecDB)
};

#endif // CTIMINGDB_H
