#ifndef CFLUORESCENCEINTERFERENCE_H
#define CFLUORESCENCEINTERFERENCE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: system
  * Date: 2024-12-19
  * Description: 荧光干扰
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QTextBrowser>
#include <QStackedWidget>
#include <QTableWidget>
#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"
#include "CLightOneTiming.h"
#include "CBusyProgressBar.h"

class CFluorescenceInterference : public QWidget, public CCmdBase
{
    Q_OBJECT
public:
    explicit CFluorescenceInterference(QWidget* parent = nullptr);
    ~CFluorescenceInterference();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant& qVarData) override;
    void ClearAllData(void);
    void SetFluorescenceType(int iTypeIndex);  // 设置荧光类型：0-荧光片，1-染料

    // 公共接口，供外部调用开始计算
    void startCalculation(int colorIndex, int machineIndex);
    // 公共接口，供外部调用结束计算
    void endCalculation();

signals:
    void sigLightTestStarted(int colorIndex, int machineIndex);  // 采光测试开始信号
    void sigLightTestEnded();  // 采光测试结束信号

private slots:
    void _SlotFamMDTBtn();
    void _SlotHexMDTBtn();
    void _SlotRoxMDTBtn();
    void _SlotCY5MDTBtn();
    void _SlotMachineChanged(int iMachineID);
    void _SlotTimingEnd(void);
    void _SlotTimingStopped(void);   // 时序停止槽函数
    void _SlotStopTest(void);        // 停止测试槽函数

private:
    void _InitWidget();
    void _SetTableItem(int iRow, int iCol, QString strText);
    void _SetTableWidget(int iRow, int iCol, int iWidth, int iHeight, QWidget *pUIWidget);
    void _ReceiveMDTData(const QVariant &qVarData);
    bool _SaveXlsxData(int colorIndex);
    
private:
    typedef struct{
        double value[4];
    }sHoleFl_t;

    CLightOneTiming *m_pLightOneTiming;
    QList <sHoleFl_t> m_flHole1Vlist;
    QList <sHoleFl_t> m_flHole2Vlist;
    int m_colorIndex;
    bool bInterfereTested;

    CLabelComboBox *m_pMachineComboBox;
    CBusyProgressBar *m_pCBusyProgressBar;
    QPushButton *m_pFamMDTBtn, *m_pHexMDTBtn, *m_pRoxMDTBtn, *m_pCY5MDTBtn;
    QTableWidget *m_pTableWidget;
    int m_iFluorescenceType;

};

#endif // CFLUORESCENCEINTERFERENCE_H 