#ifndef CHOMEDEVITEMWIDGET_H
#define CHOMEDEVITEMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-17
  * Description: 具体设备带编号
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTime>
#include <QTimer>
#include <QLabel>
#include <QWidget>
#include <QGroupBox>
#include <QPushButton>

#include "CLabelLabel.h"
#include "PublicParams.h"

#include "HistoryPage/CHistoryDetailWidget.h"

class CHomeDevItemWidget : public QWidget
{
    Q_OBJECT
public:
    CHomeDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent = nullptr);

    void SetDeviceStatus(DeviceStatus eStatus);
    void SetCardSampleInfo(const SCardInfoStruct &sCardInfo, const SSampleInfoStruct &sSampleInfo);
    void SetFLDataMap(int iMachineID, const QList<QMap<double, double>> &dFLMap);
    void SetMeltingFLDataMap(int iMachineID, const QList<double> &dTempList, const QList<QMap<double, double>> &dFLMap);
    void ClearData();

public slots:
    void SlotUpdateItemStatus(int iMachineID, DeviceStatus eStatus);
    void SlotUpdateHeartStatus(int iMachineID, DeviceStatus eStatus, bool bCardExist);
    void SlotResetStatus(int iMachineID);
    void SlotSelfTestEnd(int iMachineID, int iResult);

protected:
    virtual void mousePressEvent(QMouseEvent *pEvent) override;

signals:
    void SignalCreateTest(int iMachineID);
    void SingalDetailWidgetShow(int iMachineID);
    void SingalClearFlData(int iMachineID);

private slots:
    void _SlotActionBtn();
    void _SlotRunTimer();
    void _SlotProcessingTimer();

private:
    void _ClearData();
    void _DetailWidgetShow();

private:
    void _InitTitleLabel();
    void _InitActButton();
    QGroupBox *_CreateGroupBox();

private:
    QLabel *m_pTitleLabel;
    QLabel *m_pIndexLabel, *m_pTipsLabel, *m_pTimeLabel;
    CLabelLabel *m_pSampleIDLabel, *m_pCardIDLabel, *m_pProjectLabel;
    CLabelLabel *m_pNameLabel, *m_pGenderLabel;

    QLabel *m_pActIconLabel, *m_pActTextLabel;
    QPushButton *m_pActionBtn;

    //CHistoryDetailWidget *m_pDetailWidget;

private:
    int m_iMachineID;
    SDevParamsStruct m_sDevParams;

    bool m_bHasCardbox; //下位机是否有卡盒
    DeviceStatus m_eLastStatus;
    DeviceStatus m_eCurrentStatus;
    DeviceStatus m_eLastHeartbeatStatus;
    QTime m_qRemainTime;
    QTimer *m_pRunTimer;
    QTimer *m_pProcessingTimer;

    bool m_bHrmTest;
    int m_iRunSecond;
    SCardInfoStruct m_sCardInfo;
    SSampleInfoStruct m_sSampleInfo;
};

#endif // CHOMEDEVITEMWIDGET_H
