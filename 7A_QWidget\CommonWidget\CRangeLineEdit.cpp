#include "CRangeLineEdit.h"
#include <QBoxLayout>
#include <QRegularExpressionValidator>

CRangeLineEdit::CRangeLineEdit(QString strName, QWidget *parent) : QWidget(parent)
{
    QRegularExpressionValidator *pValidator = new QRegularExpressionValidator(QRegularExpression("[0-9]*"), this);

    m_pNameLabel = new QLabel(strName);
    m_pNameLabel->setFixedSize(210, 50);

    m_pMinLineEdit = new CLineEdit;
    m_pMinLineEdit->setFixedSize(80, 50);
    m_pMinLineEdit->setAlignment(Qt::AlignCenter);
    m_pMinLineEdit->setValidator(pValidator);

    m_pLineLabel = new QLabel("-");
    m_pLineLabel->setFixedSize(10, 50);
    m_pLineLabel->setAlignment(Qt::AlignCenter);

    m_pMaxLineEdit = new CLineEdit;
    m_pMaxLineEdit->setFixedSize(80, 50);
    m_pMaxLineEdit->setAlignment(Qt::AlignCenter);
    m_pMaxLineEdit->setValidator(pValidator);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pNameLabel);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pMinLineEdit);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pLineLabel);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pMaxLineEdit);
    this->setLayout(pLayout);
}

CRangeLineEdit::~CRangeLineEdit()
{

}

void CRangeLineEdit::ResetLabelSize(int iWidth, int iHeight)
{
    m_pNameLabel->setFixedSize(iWidth, iHeight);
}

double CRangeLineEdit::GetMinValue() const
{
    return m_pMinLineEdit->text().toDouble();
}

double CRangeLineEdit::GetMaxValue() const
{
    return m_pMaxLineEdit->text().toDouble();
}

void CRangeLineEdit::GetRangeValue(double &dMinValue, double &dMaxValue) const
{
    dMinValue = m_pMinLineEdit->text().toDouble();
    dMaxValue = m_pMaxLineEdit->text().toDouble();
}

void CRangeLineEdit::SetRangeValue(const double &dMinValue, const double &dMaxValue)
{
    m_pMinLineEdit->setText(QString::number(dMinValue));
    m_pMaxLineEdit->setText(QString::number(dMaxValue));
}

void CRangeLineEdit::Clear()
{
    m_pMinLineEdit->clear();
    m_pMaxLineEdit->clear();
}
