#include <QTimer>
#include <QDebug>
#include "CLightOneTiming.h"
#include "CMessageBox.h"

const QList<int> cLightTimingTypePre = { Method_PCRPR, Method_PM3PR };
const QList<int> cLightTimingTypeLoop = { Method_OFSTRST, Method_FLMST, Method_OFCOMP, Method_FLMSP };
const QList<int> cLightTimingTypePost = { Method_PCRRST, Method_PM2PR };

CLightOneTiming::CLightOneTiming(QWidget* parent)
    : QWidget(parent)
{
    Register2Map(Method_OFSTRST);
    Register2Map(Method_OFCOMP);
    Register2Map(Method_FLMST);
    Register2Map(Method_FLMSP);
    Register2Map(Method_PCRPR);
    Register2Map(Method_PM3PR);
    Register2Map(Method_PCRRST);
    Register2Map(Method_PM2PR);

    m_lightTimingInfo.timingState = eLightTimingState_Idle;
}

CLightOneTiming::~CLightOneTiming()
{
    UnRegister2Map(Method_OFSTRST);
    UnRegister2Map(Method_OFCOMP);
    UnRegister2Map(Method_FLMST);
    UnRegister2Map(Method_FLMSP);
    UnRegister2Map(Method_PCRPR);
    UnRegister2Map(Method_PM3PR);
    UnRegister2Map(Method_PCRRST);
    UnRegister2Map(Method_PM2PR);
}

void CLightOneTiming::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant& qVarData)
{

    if (iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if (m_lightTimingInfo.timingState == eLightTimingState_WaitRet) {
        if (iMethodID == Method_OFSTRST || iMethodID == Method_OFCOMP ||
            iMethodID == Method_FLMST || iMethodID == Method_FLMSP ||
            iMethodID == Method_PCRPR || iMethodID == Method_PM3PR ||
            iMethodID == Method_PCRRST || iMethodID == Method_PM2PR) {
            if (iResult == 0) {
                timingSendNext(iMachineID);
            }
        }
    }
}

void CLightOneTiming::startTiming(int iMachineID, int cycle)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index = 0;
    m_lightTimingInfo.phase = eTimingPhase_OnlyLoop;  // startTiming直接进入循环阶段
    m_iCurrentTiming = cLightTimingTypeLoop;
    int iMethodID = m_iCurrentTiming[m_lightTimingInfo.index];
    QString strCmd = GetJsonCmdString(iMethodID);
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;
    m_lightTimingInfo.timingLength = m_iCurrentTiming.size();
    m_lightTimingInfo.cycle = cycle;
    m_lightTimingInfo.cycleCount = 1;
}

void CLightOneTiming::startTimingType2(int iMachineID, int cycle)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index = 0;
    m_lightTimingInfo.phase = eTimingPhase_Pre;
    m_iCurrentTiming = cLightTimingTypePre;
    int iMethodID = m_iCurrentTiming[m_lightTimingInfo.index];
    QString strCmd = GetJsonCmdString(iMethodID);
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;
    m_lightTimingInfo.timingLength = m_iCurrentTiming.size();
    m_lightTimingInfo.cycle = cycle;
    m_lightTimingInfo.cycleCount = 1;
}

void CLightOneTiming::startPreTiming(int iMachineID)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index = 0;
    m_lightTimingInfo.phase = eTimingPhase_OnlyPre;
    m_iCurrentTiming = cLightTimingTypePre;
    int iMethodID = m_iCurrentTiming[m_lightTimingInfo.index];
    QString strCmd = GetJsonCmdString(iMethodID);
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;
    m_lightTimingInfo.timingLength = m_iCurrentTiming.size();
    m_lightTimingInfo.cycle = 1;
    m_lightTimingInfo.cycleCount = 1;
}

void CLightOneTiming::startPostTiming(int iMachineID)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index = 0;
    m_lightTimingInfo.phase = eTimingPhase_OnlyPost;
    m_iCurrentTiming = cLightTimingTypePost;
    int iMethodID = m_iCurrentTiming[m_lightTimingInfo.index];
    QString strCmd = GetJsonCmdString(iMethodID);
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;
    m_lightTimingInfo.timingLength = m_iCurrentTiming.size();
    m_lightTimingInfo.cycle = 1;
    m_lightTimingInfo.cycleCount = 1;
}

void CLightOneTiming::stopTiming()
{
    if (m_lightTimingInfo.timingState == eLightTimingState_WaitRet) {
        qDebug() << "停止时序执行";
        m_lightTimingInfo.timingState = eLightTimingState_Idle;
    }
    emit this->SignalTimingStopped();
}


void CLightOneTiming::timingSendNext(int iMachineID)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index++;

    // 检查当前阶段是否完成
    if (m_lightTimingInfo.index >= m_lightTimingInfo.timingLength) {
        // 根据当前阶段决定下一步操作
        if (m_lightTimingInfo.phase == eTimingPhase_Pre) {
            // 前置阶段的所有指令都执行完成，进入循环阶段
            m_lightTimingInfo.phase = eTimingPhase_Loop;
            m_lightTimingInfo.index = 0;
            m_lightTimingInfo.cycleCount = 1;
            m_iCurrentTiming = cLightTimingTypeLoop;
            m_lightTimingInfo.timingLength = m_iCurrentTiming.size();
        }
        else if (m_lightTimingInfo.phase == eTimingPhase_Loop) {
            // 循环阶段完成一轮
            if (m_lightTimingInfo.cycleCount < m_lightTimingInfo.cycle) {
                // 继续下一轮循环
                m_lightTimingInfo.cycleCount++;
                m_lightTimingInfo.index = 0;
            }
            else {
                // startTimingType2调用，进入后置阶段
                m_lightTimingInfo.phase = eTimingPhase_Post;
                m_lightTimingInfo.index = 0;
                m_iCurrentTiming = cLightTimingTypePost;
                m_lightTimingInfo.timingLength = m_iCurrentTiming.size();
            }
        }
        else if (m_lightTimingInfo.phase == eTimingPhase_OnlyLoop) {
            // 循环阶段完成一轮
            if (m_lightTimingInfo.cycleCount < m_lightTimingInfo.cycle) {
                // 继续下一轮循环
                m_lightTimingInfo.cycleCount++;
                m_lightTimingInfo.index = 0;
            }
            else {
                // startTiming调用，没有后置阶段，直接结束
                m_lightTimingInfo.timingState = eLightTimingState_End;
                emit this->SignalTimingEnd();
                return;
            }
        }
        else if (m_lightTimingInfo.phase == eTimingPhase_Post) {
            // 后置阶段完成，结束时序
            m_lightTimingInfo.timingState = eLightTimingState_End;
            emit this->SignalTimingEnd();
            return;
        }
        else if (m_lightTimingInfo.phase == eTimingPhase_OnlyPre) {
            // 仅前置阶段完成，发送前置完成信号
            m_lightTimingInfo.timingState = eLightTimingState_End;
            emit this->SignalPreTimingEnd();
            return;
        }
        else if (m_lightTimingInfo.phase == eTimingPhase_OnlyPost) {
            // 仅后置阶段完成，结束时序
            m_lightTimingInfo.timingState = eLightTimingState_End;
            emit this->SignalPostTimingEnd();
            return;
        }
    }

    int iMethodID = m_iCurrentTiming[m_lightTimingInfo.index];
    QString strCmd;

    if (iMethodID == Method_FLMST) {
        QVariantList qVarList;
        qVarList.push_back(2);
        strCmd = GetJsonCmdString(iMethodID, qVarList);
    }
    else
    {
        strCmd = GetJsonCmdString(iMethodID);
    }

    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;
}