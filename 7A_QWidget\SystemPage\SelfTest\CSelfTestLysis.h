#ifndef CSELFTESTLYSIS_H
#define CSELFTESTLYSIS_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-07-14
  * Description: 热裂解自检
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "PublicParams.h"
#include "CCmdBase.h"

class CSelfTestLysis : public QObject , public CCmdBase
{
    Q_OBJECT
public:
    explicit CSelfTestLysis(QObject *parent = nullptr);
    ~CSelfTestLysis();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

    void StartSelfTest(int iMachineID, QString strXlsxPath);
    void EndSelfTest(int iMachineID);

signals:
    void SignalSelfTestResult(int iMachineID, int iResult);

private:
    void _HandleHTInfo(int iMachineID, const QVariant &qVarData);
    void _CheckTempRate(int iMachineID, int index, double dTemp);
    void _WriteXlsx(int iMachineID);

private:
    struct SSelfTestInfoStruct
    {
        SSelfTestInfoStruct()
        {
            bStart = false;
            for(int i=0; i<4; i++)
            {
                bHasCheckVec.push_back(false);
                bResultVec.push_back(false);
                dRateVec.push_back(0);
                iTempMap[i] = QVector<double>();
            }
        }

        void Clear()
        {
            bStart = false;
            strXlsxPath.clear();
            strDetails.clear();
            dTimeVec.clear();

            for(int i=0; i<4; i++)
            {
                bHasCheckVec[i] = false;
                bResultVec[i] = false;
                dRateVec[i] = 0;
                iTempMap[i].clear();
            }
        }

        bool bStart;                         //是否启动自检
        QString strXlsxPath;                 //内部自检结果表
        QString strDetails;                  //记录升降温速率
        QDateTime qBeginTime;                //起始时间
        QVector<double> dTimeVec;            //时间
        QVector<bool> bHasCheckVec;          //是否已经检测过
        QVector<bool> bResultVec;            //是否检测通过, 3个模块全部通过才算自检通过
        QVector<double> dRateVec;            //升温速率
        QMap<int, QVector<double>> iTempMap; //4个模块温度
    };

private:
    SLysisSelfTestParamsStruct m_sParamsStruct;
    QList<SSelfTestInfoStruct *> m_pInfoStructList;
};

#endif // CSELFTESTLYSIS_H
