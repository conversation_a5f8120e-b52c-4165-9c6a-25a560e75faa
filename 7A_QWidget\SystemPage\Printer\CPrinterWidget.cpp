#include "CPrinterWidget.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QTextCursor>

#include "CRunTest.h"
#include "CMessageBox.h"
#include "CConfigJson.h"
#include "PublicParams.h"
#include "PublicFunction.h"
#include "MDControl/CPrintThread.h"

CPrinterWidget::CPrinterWidget(QWidget *parent) : QWidget(parent) , m_bSelectAll(false)
{
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/system/printer.qss");

    m_pAllCheckBoxList = this->findChildren<QCheckBox *>();
    for(int i=0; i<m_pAllCheckBoxList.size(); i++)
        connect(m_pAllCheckBoxList.at(i), &QCheckBox::clicked, this, &CPrinterWidget::_SlotCheckBoxClicked);

    _ReadCfg();

    CRunTest::GetInstance()->UpdatePrintInfo(m_sPrintStruct);
    CPrintThread::GetInstance()->UpdatePrintInfo(m_sPrintStruct);
}

void CPrinterWidget::showEvent(QShowEvent *pEvent)
{
    _ReadCfg();

    QWidget::showEvent(pEvent);
}

void CPrinterWidget::_SlotCheckBoxClicked(bool bChecked)
{
    Q_UNUSED(bChecked);

    if(m_sPrintStruct.bReportTitle != m_pReportTitleCheckBox->isChecked()
            || m_sPrintStruct.bSampleID != m_pSampleIDCheckBox->isChecked()
            || m_sPrintStruct.bSampleType != m_pSampleTypeCheckBox->isChecked()
            || m_sPrintStruct.bSamplingDate != m_pSamplingDateCheckBox->isChecked()
            || m_sPrintStruct.bTestTime != m_pTestTimeCheckBox->isChecked()
            || m_sPrintStruct.bCardID != m_pCardIDCheckBox->isChecked()
            || m_sPrintStruct.bCardLot != m_pCardLotCheckBox->isChecked()
            || m_sPrintStruct.bCardMFG != m_pCardMFGCheckBox->isChecked()
            || m_sPrintStruct.bCardEXP != m_pCardEXPCheckBox->isChecked()
            || m_sPrintStruct.bName != m_pNameCheckBox->isChecked()
            || m_sPrintStruct.bGender != m_pGenderCheckBox->isChecked()
            || m_sPrintStruct.bAge != m_pAgeCheckBox->isChecked()
            || m_sPrintStruct.bBirthday != m_pBirthdayCheckBox->isChecked()
            || m_sPrintStruct.bTelephone != m_pTelephoneCheckBox->isChecked()
            || m_sPrintStruct.bOperator != m_pOperatorCheckBox->isChecked()
            || m_sPrintStruct.bCT != m_pCTCheckBox->isChecked()
            || m_sPrintStruct.bStatement != m_pStatementCheckBox->isChecked()
            || m_sPrintStruct.bAutoPrint != m_pAutoPrintCheckBox->isChecked())
    {
        //m_pSaveBtn->setEnabled(true);
    }
    else
    {
        //m_pSaveBtn->setEnabled(false);
    }

    _CheckAllClicked();
    _UpdatePreviewText();
}

void CPrinterWidget::_SlotTextChanged(const QString &strText)
{
    Q_UNUSED(strText);

    if(m_sPrintStruct.strReportTitle != m_pReportTitleLineEdit->text())
        m_pSaveBtn->setEnabled(true);

    if(m_sPrintStruct.strStatement != m_pStatementLineEdit->text())
        m_pSaveBtn->setEnabled(true);

    _UpdatePreviewText();
}

void CPrinterWidget::_SlotSelectAllBtn()
{
    m_bSelectAll = !m_bSelectAll;
    if(m_bSelectAll)
        m_pSelectAllBtn->setText(tr("取消全选"));
    else
        m_pSelectAllBtn->setText(tr("全选"));

    for(int i=0; i<m_pAllCheckBoxList.size(); i++)
        m_pAllCheckBoxList.at(i)->setChecked(m_bSelectAll);

    _SlotCheckBoxClicked(m_bSelectAll);
    _UpdatePreviewText();
}

void CPrinterWidget::_SlotSaveBtn()
{
    m_sPrintStruct.bReportTitle = m_pReportTitleCheckBox->isChecked();
    m_sPrintStruct.strReportTitle = m_pReportTitleLineEdit->text();

    m_sPrintStruct.bSampleID = m_pSampleIDCheckBox->isChecked();
    m_sPrintStruct.bSampleType = m_pSampleTypeCheckBox->isChecked();
    m_sPrintStruct.bSamplingDate = m_pSamplingDateCheckBox->isChecked();

    m_sPrintStruct.bTestTime = m_pTestTimeCheckBox->isChecked();
    m_sPrintStruct.bCardID = m_pCardIDCheckBox->isChecked();
    m_sPrintStruct.bCardLot = m_pCardLotCheckBox->isChecked();

    m_sPrintStruct.bCardMFG = m_pCardMFGCheckBox->isChecked();
    m_sPrintStruct.bCardEXP = m_pCardEXPCheckBox->isChecked();
    m_sPrintStruct.bName = m_pNameCheckBox->isChecked();

    m_sPrintStruct.bGender = m_pGenderCheckBox->isChecked();
    m_sPrintStruct.bAge = m_pAgeCheckBox->isChecked();
    m_sPrintStruct.bBirthday = m_pBirthdayCheckBox->isChecked();

    m_sPrintStruct.bTelephone = m_pTelephoneCheckBox->isChecked();
    m_sPrintStruct.bOperator = m_pOperatorCheckBox->isChecked();
    m_sPrintStruct.bCT = m_pCTCheckBox->isChecked();

    m_sPrintStruct.bAutoPrint = m_pAutoPrintCheckBox->isChecked();

    m_sPrintStruct.bStatement = m_pStatementCheckBox->isChecked();
    m_sPrintStruct.strStatement = m_pStatementLineEdit->text();

    QJsonObject qPrintObj;

    qPrintObj.insert("bReportTitle", m_sPrintStruct.bReportTitle);
    qPrintObj.insert("strReportTitle", m_sPrintStruct.strReportTitle);

    qPrintObj.insert("bSampleID", m_sPrintStruct.bSampleID);
    qPrintObj.insert("bSampleType", m_sPrintStruct.bSampleType);
    qPrintObj.insert("bSamplingDate", m_sPrintStruct.bSamplingDate);

    qPrintObj.insert("bTestTime", m_sPrintStruct.bTestTime);
    qPrintObj.insert("bCardID", m_sPrintStruct.bCardID);
    qPrintObj.insert("bCardSN", m_sPrintStruct.bCardLot);

    qPrintObj.insert("bCardMFG", m_sPrintStruct.bCardMFG);
    qPrintObj.insert("bCardEXP", m_sPrintStruct.bCardEXP);
    qPrintObj.insert("bName", m_sPrintStruct.bName);

    qPrintObj.insert("bGender", m_sPrintStruct.bGender);
    qPrintObj.insert("bAge", m_sPrintStruct.bAge);
    qPrintObj.insert("bBirthday", m_sPrintStruct.bBirthday);

    qPrintObj.insert("bTelephone", m_sPrintStruct.bTelephone);
    qPrintObj.insert("bOperator", m_sPrintStruct.bOperator);
    qPrintObj.insert("bCT", m_sPrintStruct.bCT);

    qPrintObj.insert("bAutoPrint", m_sPrintStruct.bAutoPrint);

    qPrintObj.insert("bStatement", m_sPrintStruct.bStatement);
    qPrintObj.insert("strStatement", m_sPrintStruct.strStatement);

    CConfigJson::GetInstance()->SetConfigJsonObject("Print", qPrintObj);

    //m_pSaveBtn->setEnabled(false);
    ShowSuccess(this, tr("提示"), tr("保存成功"));

    CRunTest::GetInstance()->UpdatePrintInfo(m_sPrintStruct);
    CPrintThread::GetInstance()->UpdatePrintInfo(m_sPrintStruct);
}

void CPrinterWidget::_ReadCfg()
{
    QJsonObject qPrintObj = CConfigJson::GetInstance()->GetConfigJsonObject("Print");
    if(qPrintObj.isEmpty())
    {
        qDebug()<<Q_FUNC_INFO<<"使用默认打印选项";
        m_sPrintStruct = SPrintInfoStruct();
    }
    else
    {
        m_sPrintStruct.bReportTitle = qPrintObj.value("bReportTitle").toBool();
        m_sPrintStruct.strReportTitle = qPrintObj.value("strReportTitle").toString();

        m_sPrintStruct.bSampleID = qPrintObj.value("bSampleID").toBool();
        m_sPrintStruct.bSampleType = qPrintObj.value("bSampleType").toBool();
        m_sPrintStruct.bSamplingDate = qPrintObj.value("bSamplingDate").toBool();

        m_sPrintStruct.bTestTime = qPrintObj.value("bTestTime").toBool();
        m_sPrintStruct.bCardID = qPrintObj.value("bCardID").toBool();
        m_sPrintStruct.bCardLot = qPrintObj.value("bCardSN").toBool();

        m_sPrintStruct.bCardMFG = qPrintObj.value("bCardMFG").toBool();
        m_sPrintStruct.bCardEXP = qPrintObj.value("bCardEXP").toBool();
        m_sPrintStruct.bName = qPrintObj.value("bName").toBool();

        m_sPrintStruct.bGender = qPrintObj.value("bGender").toBool();
        m_sPrintStruct.bAge = qPrintObj.value("bAge").toBool();
        m_sPrintStruct.bBirthday = qPrintObj.value("bBirthday").toBool();

        m_sPrintStruct.bTelephone = qPrintObj.value("bTelephone").toBool();
        m_sPrintStruct.bOperator = qPrintObj.value("bOperator").toBool();
        m_sPrintStruct.bCT = qPrintObj.value("bCT").toBool();

        m_sPrintStruct.bAutoPrint = qPrintObj.value("bAutoPrint").toBool();

        m_sPrintStruct.bStatement = qPrintObj.value("bStatement").toBool();
        m_sPrintStruct.strStatement = qPrintObj.value("strStatement").toString();
    }

    m_pReportTitleCheckBox->setChecked(m_sPrintStruct.bReportTitle);
    m_pReportTitleLineEdit->setText(m_sPrintStruct.strReportTitle);

    m_pSampleIDCheckBox->setChecked(m_sPrintStruct.bSampleID);
    m_pSampleTypeCheckBox->setChecked(m_sPrintStruct.bSampleType);
    m_pSamplingDateCheckBox->setChecked(m_sPrintStruct.bSamplingDate);

    m_pTestTimeCheckBox->setChecked(m_sPrintStruct.bTestTime);
    m_pCardIDCheckBox->setChecked(m_sPrintStruct.bCardID);
    m_pCardLotCheckBox->setChecked(m_sPrintStruct.bCardLot);

    m_pCardMFGCheckBox->setChecked(m_sPrintStruct.bCardMFG);
    m_pCardEXPCheckBox->setChecked(m_sPrintStruct.bCardEXP);
    m_pNameCheckBox->setChecked(m_sPrintStruct.bName);

    m_pGenderCheckBox->setChecked(m_sPrintStruct.bGender);
    m_pAgeCheckBox->setChecked(m_sPrintStruct.bAge);
    m_pBirthdayCheckBox->setChecked(m_sPrintStruct.bBirthday);

    m_pTelephoneCheckBox->setChecked(m_sPrintStruct.bTelephone);
    m_pOperatorCheckBox->setChecked(m_sPrintStruct.bOperator);
    m_pCTCheckBox->setChecked(m_sPrintStruct.bCT);

    m_pAutoPrintCheckBox->setChecked(m_sPrintStruct.bAutoPrint);

    m_pStatementCheckBox->setChecked(m_sPrintStruct.bStatement);
    m_pStatementLineEdit->setText(m_sPrintStruct.strStatement);    

    _CheckAllClicked();
    _UpdatePreviewText();

    //m_pSaveBtn->setEnabled(false);
}

void CPrinterWidget::_CheckAllClicked()
{
    for(int i=0; i<m_pAllCheckBoxList.size(); i++)
    {
        if(!m_pAllCheckBoxList.at(i)->isChecked())
        {
            m_bSelectAll = false;
            break;
        }
        else
        {
            m_bSelectAll = true;
        }
    }

    if(m_bSelectAll)
        m_pSelectAllBtn->setText(tr("取消全选"));
    else
        m_pSelectAllBtn->setText(tr("全选"));
}

void CPrinterWidget::_UpdatePreviewText()
{
    m_pPreviewTextEdit->clear();
    m_pPreviewTextEdit->append("");

    if(m_pReportTitleCheckBox->isChecked())
        m_pPreviewTextEdit->append(m_pReportTitleLineEdit->text());
    m_pPreviewTextEdit->append("----------------------------------------");

    if(m_pSampleIDCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("样本编号：") + "*********");

    if(m_pSampleTypeCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("样本类型：咽拭子"));

    if(m_pSamplingDateCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("采样日期：") + "2024-08-16");

    if(m_pTestTimeCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("测试时间：") + "2024-08-16 14:00:00");

    if(m_pCardIDCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("试剂卡编号：") + "*********");

    if(m_pCardLotCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("试剂卡批次：") + "*********");

    if(m_pCardMFGCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("试剂卡生产日期：") + "2024-07-01");

    if(m_pCardEXPCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("试剂卡有效日期：") + "2025-07-01");

    if(m_pNameCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("姓名：张三"));

    if(m_pGenderCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("性别：男"));

    if(m_pAgeCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("年龄：20岁"));

    if(m_pBirthdayCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("生日：") + "2004-04-24");

    if(m_pTelephoneCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("电话：") + "18877771111");

    if(m_pOperatorCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("操作者：Admin"));

    if(m_pCTCheckBox->isChecked())
    {
        m_pPreviewTextEdit->append("----------------------------------------");
        m_pPreviewTextEdit->append("2019-nCoV N gene：" + tr("阴性") + " (Ct: N/A)");
        m_pPreviewTextEdit->append("2019-nCoV ORF1ab gene：" + tr("阴性") + " (Ct: N/A)");
        m_pPreviewTextEdit->append("FluA：" + tr("阳性") + " (Ct: 27.35)");
        m_pPreviewTextEdit->append("FluB：" + tr("阴性") + " (Ct: N/A))");
    }
    else
    {
        m_pPreviewTextEdit->append("----------------------------------------");
        m_pPreviewTextEdit->append("2019-nCoV N gene：" + tr("阴性"));
        m_pPreviewTextEdit->append("2019-nCoV ORF1ab gene：" + tr("阴性"));
        m_pPreviewTextEdit->append("FluA：" + tr("阳性"));
        m_pPreviewTextEdit->append("FluB：" + tr("阴性"));
    }

    m_pPreviewTextEdit->append("----------------------------------------");
    if(m_pStatementCheckBox->isChecked())
        m_pPreviewTextEdit->append(tr("实验声明：") + m_pStatementLineEdit->text());

    QTextCursor qCursor = m_pPreviewTextEdit->textCursor();
    qCursor.movePosition(QTextCursor::Start);
    m_pPreviewTextEdit->setTextCursor(qCursor);
}

void CPrinterWidget::_InitWidget()
{
    m_pCSysFirstTtileWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("打印设置"));
    connect(m_pCSysFirstTtileWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CPrinterWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCSysSecondTitleWidget = new CSysSecondTitleWidget({tr("打印选项"), tr("外部打印机")});

    m_pReportTitleCheckBox = new QCheckBox(tr("报告单标题"));

    m_pReportTitleLineEdit = new CLineEdit;
    m_pReportTitleLineEdit->setFixedSize(915, 56);
    connect(m_pReportTitleLineEdit, &CLineEdit::textChanged, this, &CPrinterWidget::_SlotTextChanged);

    m_pStatementCheckBox = new QCheckBox(tr("实验声明"));

    m_pStatementLineEdit = new CLineEdit;
    m_pStatementLineEdit->setFixedSize(915, 56);
    connect(m_pStatementLineEdit, &CLineEdit::textChanged, this, &CPrinterWidget::_SlotTextChanged);

    m_pSampleIDCheckBox = new QCheckBox(tr("样本编号"));
    m_pSampleTypeCheckBox = new QCheckBox(tr("样本类型"));
    m_pSamplingDateCheckBox = new QCheckBox(tr("采样日期"));

    m_pTestTimeCheckBox = new QCheckBox(tr("测试时间"));
    m_pCardIDCheckBox = new QCheckBox(tr("试剂卡编号"));
    m_pCardLotCheckBox = new QCheckBox(tr("试剂卡批次"));

    m_pCardMFGCheckBox = new QCheckBox(tr("试剂卡生产日期"));
    m_pCardEXPCheckBox = new QCheckBox(tr("试剂卡有效日期"));
    m_pNameCheckBox = new QCheckBox(tr("姓名"));

    m_pGenderCheckBox = new QCheckBox(tr("性别"));
    m_pAgeCheckBox = new QCheckBox(tr("年龄"));
    m_pBirthdayCheckBox = new QCheckBox(tr("生日"));

    m_pTelephoneCheckBox = new QCheckBox(tr("电话"));
    m_pOperatorCheckBox = new QCheckBox(tr("操作者"));
    m_pCTCheckBox = new QCheckBox(tr("Ct值"));

    m_pAutoPrintCheckBox = new QCheckBox(tr("自动打印"));

    m_pVLabel = new QLabel;
    m_pVLabel->setFixedSize(1, 705);
    m_pVLabel->setObjectName("LineLabel");

    m_pPreviewTextEdit = new QTextEdit;
    m_pPreviewTextEdit->setFixedSize(484, 705);
    m_pPreviewTextEdit->setReadOnly(true);
    m_pPreviewTextEdit->setPlaceholderText(tr("打印预览"));

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CPrinterWidget::SignalReturn);

    m_pSelectAllBtn = new QPushButton(tr("全选"));
    m_pSelectAllBtn->setFixedSize(150, 56);
    connect(m_pSelectAllBtn, &QPushButton::clicked, this, &CPrinterWidget::_SlotSelectAllBtn);

    m_pSaveBtn = new QPushButton(tr("保存"));
    m_pSaveBtn->setFixedSize(150, 56);
    connect(m_pSaveBtn, &QPushButton::clicked, this, &CPrinterWidget::_SlotSaveBtn);

    if(eLanguage_English == gk_iLanguage)
    {
        int iWidth = 360;
        m_pSampleIDCheckBox->setFixedWidth(iWidth);
        m_pSampleTypeCheckBox->setFixedWidth(iWidth);
        m_pSamplingDateCheckBox->setFixedWidth(iWidth);

        m_pTestTimeCheckBox->setFixedWidth(iWidth);
        m_pCardIDCheckBox->setFixedWidth(iWidth);
        m_pCardLotCheckBox->setFixedWidth(iWidth);

        m_pCardMFGCheckBox->setFixedWidth(iWidth);
        m_pCardEXPCheckBox->setFixedWidth(iWidth);
        m_pNameCheckBox->setFixedWidth(iWidth);

        m_pGenderCheckBox->setFixedWidth(iWidth);
        m_pAgeCheckBox->setFixedWidth(iWidth);
        m_pBirthdayCheckBox->setFixedWidth(iWidth);

        m_pTelephoneCheckBox->setFixedWidth(iWidth);
        m_pOperatorCheckBox->setFixedWidth(iWidth);
        m_pCTCheckBox->setFixedWidth(iWidth);

        m_pAutoPrintCheckBox->setFixedWidth(iWidth);

        m_pReportTitleLineEdit->setFixedSize(890, 56);
        m_pStatementLineEdit->setFixedSize(980, 56);

        m_pReturnBtn->setFixedSize(170, 56);
        m_pSelectAllBtn->setFixedSize(170, 56);
        m_pSaveBtn->setFixedSize(170, 56);
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        m_pSelectAllBtn->setFixedSize(250, 56);
        m_pReportTitleLineEdit->setFixedSize(850, 56);
        m_pStatementLineEdit->setFixedSize(970, 56);
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        m_pSelectAllBtn->setFixedSize(370, 56);
        m_pReportTitleLineEdit->setFixedSize(850, 56);
        m_pStatementLineEdit->setFixedSize(910, 56);
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        m_pSelectAllBtn->setFixedSize(230, 56);
        m_pReportTitleLineEdit->setFixedSize(780, 56);
        m_pStatementLineEdit->setFixedSize(960, 56);
    }
}

void CPrinterWidget::_InitLayout()
{
    QHBoxLayout *pReportLayout = new QHBoxLayout;
    pReportLayout->setMargin(0);
    pReportLayout->setSpacing(0);
    pReportLayout->addWidget(m_pReportTitleCheckBox);
    pReportLayout->addSpacing(20);
    pReportLayout->addStretch(1);
    pReportLayout->addWidget(m_pReportTitleLineEdit);

    QHBoxLayout *pStatementLayout = new QHBoxLayout;
    pStatementLayout->setMargin(0);
    pStatementLayout->setSpacing(0);
    pStatementLayout->addWidget(m_pStatementCheckBox);
    pStatementLayout->addSpacing(20);
    pStatementLayout->addStretch(1);
    pStatementLayout->addWidget(m_pStatementLineEdit);

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setVerticalSpacing(50);
    pGridLayout->setHorizontalSpacing(40);
    if(eLanguage_English == gk_iLanguage)
    {
        pGridLayout->setHorizontalSpacing(20);
    }
    pGridLayout->addLayout(pReportLayout, 0, 0, 1, 3);

    pGridLayout->addWidget(m_pSampleIDCheckBox, 1, 0);
    pGridLayout->addWidget(m_pSampleTypeCheckBox, 1, 1);
    pGridLayout->addWidget(m_pSamplingDateCheckBox, 1, 2);

    pGridLayout->addWidget(m_pTestTimeCheckBox, 2, 0);
    pGridLayout->addWidget(m_pCardIDCheckBox, 2, 1);
    pGridLayout->addWidget(m_pCardLotCheckBox, 2, 2);

    pGridLayout->addWidget(m_pCardMFGCheckBox, 3, 0);
    pGridLayout->addWidget(m_pCardEXPCheckBox, 3, 1);
    pGridLayout->addWidget(m_pNameCheckBox, 3, 2);

    pGridLayout->addWidget(m_pGenderCheckBox, 4, 0);
    pGridLayout->addWidget(m_pAgeCheckBox, 4, 1);
    pGridLayout->addWidget(m_pBirthdayCheckBox, 4, 2);

    pGridLayout->addWidget(m_pTelephoneCheckBox, 5, 0);
    pGridLayout->addWidget(m_pOperatorCheckBox, 5, 1);
    pGridLayout->addWidget(m_pCTCheckBox, 5, 2);

    pGridLayout->addWidget(m_pAutoPrintCheckBox, 6, 0);
    pGridLayout->addLayout(pStatementLayout, 7, 0, 1, 3);

    QVBoxLayout *pCheckBoxLayout = new QVBoxLayout;
    pCheckBoxLayout->setMargin(0);
    pCheckBoxLayout->setSpacing(0);
    pCheckBoxLayout->addLayout(pGridLayout);
    pCheckBoxLayout->addStretch(1);

    QHBoxLayout *pPrintLayout = new QHBoxLayout;
    pPrintLayout->setMargin(0);
    pPrintLayout->setSpacing(0);
    pPrintLayout->addSpacing(24);
    pPrintLayout->addLayout(pCheckBoxLayout);
    pPrintLayout->addStretch(1);
    pPrintLayout->addWidget(m_pVLabel);
    pPrintLayout->addStretch(1);
    pPrintLayout->addWidget(m_pPreviewTextEdit);
    pPrintLayout->addSpacing(24);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(50);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pReturnBtn);
    pBtnLayout->addWidget(m_pSelectAllBtn);
    pBtnLayout->addWidget(m_pSaveBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setMargin(0);
    pBackLayout->addSpacing(24);
    pBackLayout->addLayout(pPrintLayout);
	pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    pBackLayout->addSpacing(24);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysFirstTtileWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
