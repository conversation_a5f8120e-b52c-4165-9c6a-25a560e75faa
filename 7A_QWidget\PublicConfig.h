#ifndef GLOBALCLASS_H
#define GLOBALCLASS_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-27
  * Description: set/get 配置
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMap>
#include <QObject>
#include <QDateTime>
#include <QVariant>
#include "PublicParams.h"

#define RUN_LOG(strLog) CPublicConfig::RunLog(strLog)

class CPublicConfig : public QObject
{
    Q_OBJECT
public:
    static CPublicConfig *GetInstance();
    ~CPublicConfig();

signals:
    void SignalAppStartEnd();
    void SignalReadPowerVersion();
    void SignalSetPowerVersion(QString strVersion);

    void SignalWiFiStatus(bool bConnect);
    void SignalEth1Status(bool bConnect);


    //更新 QC;仪器信息-下位机;仪器自检-设备;仪器校准-设备; 的设备状态
    void SignalSetDevStatus(int iMachineID, DeviceStatus eStatus);

    void SignalRunLog(QString strLog);

    void SignalUpdateFLData(int iMachineID, const QList<QMap<double, double>> &dFLMap);
    void SignalUpdateMeltingFLData(int iMachineID, const QList<double> &dTempMap,const QList<QMap<double, double>> &dFLMap);

    void SignalLockScreenParams(int iLockTime, bool bNeverLock);

    //时序测试开始信号: 1.清空工厂荧光曲线数据 2.清空工厂熔解曲线数据 3.清空此次测试故障码 4.清空工厂气压曲线数据
    //               5.清空工厂温控曲线数据 6.清空工厂热裂解曲线数据 7.清空工厂MDT曲线数据 8.清空测试详情曲线数据
    void SignalTimingTestStart(int iMachineID);


    void SignalTecTestStart(int iMachineID, const QString &strTecName);
    void SignalSoftTypeChanged(int);
    void SignalGetLogData(int iMachineID, int iPackID, const QByteArray &byteLogData);
    void SignalReGetMotorTextIDData();
    void SignalUpdatePCRNameInfo(const QStringList &strPCRList);
    void SignalSaveFaultCode(int iCode, int iMachineID,
                             const QString &strTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    void SignalRefreshSystemFalutLogPage();

    void SignalShowRawCurve(bool bShowRawCurve);

    //故障日志传给自检
    void SignalFaultLog(int iMachineID, QVariantList qLogList);

    //自检结束,立马更新状态为下位机上1次上传的状态.自检失败把仪器状态设为故障
    void SignalSelfTestEnd(int iMachineID, int iResult);

public:
    static void RunLog(QString strLog);

    void GetDevItemNum(int &iDevNum, int &iItemNum);

    void SetMachineStatus(int iMachineID, DeviceStatus eStatus);
    DeviceStatus GetMachineStatus(int iMachineID);

    QStringList GetMachineNameList();

    QString GetAgeDBString(const QString &strShowAge);
    QString GetAgeShowString(const QString &strDBAge);

    QString GetGenderDBString(const QString &strShowGender);
    QString GetGenderShowString(const QString &strDBGender);

    QString GetTestTypeShowString(const QString &strDBType);
    QString GetTestTypeDBString(const QString &strShowType);

    QString GetStatusShowString(int iStatus);

    QString GetTimeFormatString();

    QString GetUDiskExportDir() const;

    void SetMachineSN(const QString &strSN);
    QString GetMachineSN() const;

    void SetDynamicPassword(bool bDynamicPassword);
    bool GetDynamicPassword();

    void SetDynamicUpValue(bool bDynamicUpValue);
    bool GetDynamicUpValue();

    void SetCalcParam(bool bCalcParam);
    bool GetCalcParam();



    void SetLoginLevel(int iLevel);
    int GetLoginLevel() const;

    void SetLoginUser(const QString &strUser);
    QString GetLoginUser() const;

    void SetLoginPassword(const QString &strPassword);
    QString GetLoginPassword();

    bool GetQmlKeyboard();

    QStringList GetLogDirList() const;
    QString GetPdfDir() const;
    QString GetXlsxDir() const;

    void SetLightSNXlsxName(QString strSN);
    QString GetLightSNXlsxName();

    QString GetResourceDir() const;          //./Resources/
    QString GetLogJsonFilePath() const;      //日志配置路径log.json
    QString GetLogSaveDir() const;           //日志保存路径
    bool SetLogSaveDir(QString strDir);      //设置日志路径
    QString GetConfigFilePath() const;       //config配置文件

    QString GetMethodNameByID(int iMethodID) const; //由指令ID获取指令名称
    int GetMethodIDByName(QString strMethodName) const ;

    QStringList GetHoleNameList() const;

    QString GetTecDBPath();
    QString GetHistoryDBPath();
    QString GetMotorInfoDBPath();
    QString GetSystemDBPath();
    QString GetLogDBPath();
    QString GetTimingDBPath();
    QString GetMotorDBPath();
    QString GetUserDBPath();
    QString GetLotInfoDBPath() const;
    QString GetProjectDBPath() const;

    int GetCmdIDFromIndex(int);
    int GetTimingIndexByCmdID(int iCmdID);

    QString GetMotorMethodCHTextByID(int iMethodID);

    QStringList GetMotorNameList();
    QStringList GetExtractMotorNameList();

    void SetSoftTypeChanged(int iSoftType); //改变软件类型 主要为 0：全自动软件 1：提取工装

    void SetCheckCardExistBeforeStartTest(bool bCheck);
    bool GetCheckCardExistBeforeStartTest();

    // 获取热裂解参数
    QStringList GetPyrolysisStringList(int iCmdId);
    int GetPyroLysisParamId(int iIndex);
    int GetPyroLysisParamIndexById(int iID);

    void SetTestXlsxName(int iMachineID, const QString &strXlsxName);
    QString GetTestXlsxName(int iMachineID);

    void SetTestPdfName(int iMachineID, const QString &strPdfName);
    QString GetTestPdfName(int iMachineID);

    void SetPLCVersionStruct(int iMachineID, const SPLCVerStruct &sVerStruct);
    SPLCVerStruct GetPLCVersionStruct(int iMachineID);

    void SetRunTimeMinute(int iMinute);
    int GetRunTimeMinute();

    void SetPowerVersion(QString strPowerVersion);
    QString GetPowerVersion();

    void SetWiFiConnect(bool bConnect);
    bool GetWiFiConnect();

    void SetEthConnect(bool bConnect);
    bool GetEthConnect();

    void SetFtpAutoUpload(bool bUpload);
    bool GetFtpAutoUpload();

    void SetTestDoneAutoReset(bool bReset);
    bool GetTestDoneAutoReset();

    void InitSampleTypeMap();
    QStringList GetSampleTypeList(QString strProject);
    QString GetSampleTypeString(int iSampleType);
    int GetSampleTypeKey(QString strSampleType);

    void SetLisTwoWay(bool bTwoWay);
    bool GetLisTwoWay();

    void SetShowRawCurve(bool bShowCurve);
    bool GetShowRawCurve();
	
	 //总版本
    QString GetFullVersion();

    QString GetSelfTestJsonFilePath();

    void SetSelfTestParamsStruct(const SDeviceSelfTestParamsStruct &sParamsStruct);
    SDeviceSelfTestParamsStruct GetSelfTestParamsStruct();

    void SetSelfTestParamsJson(const QString &strJson);
    QString GetSelfTestParamsJson();

    void SetSelfTestRunning(int iMachineID, bool bRunning);
    bool GetSelfTestRunning(int iMachineID);

private:
    CPublicConfig();

    CPublicConfig(const CPublicConfig &) = delete;
    CPublicConfig &operator= (const CPublicConfig &) = delete;

    void _InitMethodIDNameMap();
    void _InitTimingIDIndexMap();
    void _InitMotorMethodIDCHTextMap();
    void _InitPyrolysisParam();
    void _InitAllSampleTypeList();

private:
    static CPublicConfig* m_spInstance;
    QString m_strTimeFormat;
    bool m_bQmlKeyboard;
    bool m_bAgingTestRun;
    QString m_strLogSaveDir;
    QString m_strDBDir;

    bool m_bDynamicPassword;
    bool m_bDynamicUpValue;
    bool m_bCalcParam {false};
    int m_iLoginLevel;
    QString m_strLoginUser;
    QString m_strLoginPassword;

    QString m_strMachineSN;
    QString m_lightSNXlsxName;

    QMap<int, QString> m_strMethodIDNameMap;
    QMap<int, int> m_strTimingIndex2IDMap;

    int m_iSoftType;
    QStringList m_strAutoMotorNameList, m_strExtractMotorNameList;

    QMap<int, QString> m_strMotorIDCHTextMap;    //methodid + method中文描述

    QList<DeviceStatus> m_eDeviceStatusList;

    int m_iDevNum, m_iItemNum;
    bool m_bCheckCardExist;

    // 热裂解参数初始化start
    QStringList m_strHTOpenList;
    QStringList m_strHTCloseList;
    QHash<int, int> m_htParamIndexIdMap;
    // 热裂解参数初始化end

    QStringList m_strXlsxNameList;
    QStringList m_strPdfNameList;

    QList<SPLCVerStruct> m_sPLCVersionList; //保存下位机模块版本

    int m_iRunTimeMinute;
    QString m_strPowerVersion;

    bool m_bWiFiConnect, m_bEthConnect;
    bool m_bFtpAutoUpload;

    bool m_bAutoReset; //测试结束是否自动复位

    //key:样本类型代号;value:样本类型文字
    QMap<int, QString> m_strAllSampleTypeMap;

    //key:项目;value:该项目所有的样本类型
    QMap<QString, QStringList> m_strProjectSampleTypeMap;

    bool m_bLisTwoWay; //双向LIS
    bool m_bShowRawCurve; //详情是否显示原始曲线
	
	QString m_strFullVersion;

    QList<bool> m_bSelfTestRunningList; //是否在自检中
    SDeviceSelfTestParamsStruct m_sSelfTestParamsStruct;

    QString m_strSelfJsonParamsJson;
};

#endif // GLOBALCLASS_H
