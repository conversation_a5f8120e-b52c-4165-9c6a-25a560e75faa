#ifndef CSELFTESTPARAMS_H
#define CSELFTESTPARAMS_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-07-21
  * Description: 自检参数
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include <QPushButton>
#include "PublicParams.h"
#include "CLabelLineEdit.h"
#include "CRangeLineEdit.h"

class CSelfTestParams : public QWidget
{
    Q_OBJECT
public:
    explicit CSelfTestParams(QWidget *parent = nullptr);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

private slots:
    void _SlotResetBtn();
    void _SlotSaveBtn();

private:
    void _ReadSelfTestJson();
    void _WriteSelfTestJson();
    void _UpdateLineEditValue();

private:
    void _InitWidget();
    void _InitLayout();

    QGroupBox *_CreateTECGroupBox();
    QGroupBox *_CreateLysisGroupBox();
    QGroupBox *_CreatePressureGroupBox();

private:
    CHLabelLineEdit *m_pTECStartTempLineEdit, *m_pTECMaxTempLineEdit, *m_pTECEndTempLineEdit;
    CHLabelLineEdit *m_pTECUpRateLineEdit, *m_pTECDownRateLineEdit, *m_pTECSpanLineEdit;
    CHLabelLineEdit *m_pLysisMaxTempLineEdit, *m_pLysisUpRateLineEdit;

    CRangeLineEdit *m_pAddTimeRangeLineEdit, *m_pAddValueRangeLineEdit;
    CRangeLineEdit *m_pKeepTimeRangeLineEdit, *m_pKeepValueRangeLineEdit;
    CRangeLineEdit *m_pSubTimeRangeLineEdit;
    CHLabelLineEdit *m_pSubMinValueLineEidt;

    CRangeLineEdit *m_pAddTimeRangeLineEdit2, *m_pAddValueRangeLineEdit2;
    CRangeLineEdit *m_pKeepTimeRangeLineEdit2, *m_pKeepValueRangeLineEdit2;
    CRangeLineEdit *m_pSubTimeRangeLineEdit2;
    CHLabelLineEdit *m_pSubMinValueLineEidt2;

    QPushButton *m_pResetBtn, *m_pSaveBtn;

    int m_iNameWidth, m_iValueWidth, m_iHeight;
    int m_iGasLeftWidth, m_iGasRightWidth;

    SDeviceSelfTestParamsStruct m_sSelfTestStruct;
};

#endif // CSELFTESTPARAMS_H
