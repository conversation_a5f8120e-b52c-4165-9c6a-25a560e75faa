#include "CRealFL.h"
#include <QTime>
#include <QStyleFactory>
#include "CRunTest.h"
#include "CHistoryDB.h"

CRealFL::CRealFL(QWidget *parent) : QWidget(parent)
{
    m_strClassName = "CRealFL";
    QTime start = QTime::currentTime();

    Register2Map(Method_fl_data);
    Register2Map(Method_pcr_signal);
    Register2Map(Method_start);

    _InitWidget();
    _InitLayout();

    QString strQSS = "QWidget{border-radius: 5px;}";
    this->setStyleSheet(strQSS);

    _ShowLast100FLID();
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTimingTestStart, this, &CRealFL::SlotTestStart);
    connect(CRunTest::GetInstance(),&CRunTest::SignalUpdateItemCalcResult,this,&CRealFL::_ShowLast100FLID);
    qDebug()<<"CRealFL 时间:"<<start.msecsTo(QTime::currentTime());
}

CRealFL::~CRealFL()
{
    UnRegister2Map(Method_fl_data);
    UnRegister2Map(Method_pcr_signal);
    UnRegister2Map(Method_start);
}

void CRealFL::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_fl_data == iMethodID)
    {
        m_pFLWidgetList.at(iMachineID)->ParseFLCmd(qVarData);
    }
    else if(Method_pcr_signal == iMethodID)
    {
        m_pFLWidgetList.at(iMachineID)->ParsePCRSignalCmd(qVarData);
    }
    else if(Method_start == iMethodID)
    {
        if(CPublicConfig::GetInstance()->GetSelfTestRunning(iMachineID))
        {
            qDebug()<<Q_FUNC_INFO<<QString("%1#自检测试不计算CT").arg(iMachineID + 1);
        }
        else
        {
            m_pFLWidgetList.at(iMachineID)->ParseStartCmd(iResult);
            _ShowLast100FLID();
        }
    }
}

void CRealFL::SlotTestStart(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    qDebug()<<QString("时序开始,清空%1#荧光数据").arg(iMachineID + 1);
    m_pFLWidgetList.at(iMachineID)->ClearData();
    m_pFLWidgetList.at(iMachineID)->InitTestInfo();
}

void CRealFL::_SlotMachineChanged(int iMachineID)
{
    m_pStackedWidget->setCurrentIndex(iMachineID);
}

void CRealFL::_SlotCardIDComboBoxChanged(const QString &strCardID)
{
    m_pCardIDLineEdit->SetLineEditText(strCardID);
}

void CRealFL::_SlotCardIDLineEditTextChanged(const QString &strCardID)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_pFLWidgetList.at(iMachineID)->SetCardID(strCardID);
}

void CRealFL::_SlotShowCurrentTestCardID(int iMachineID)
{
    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);
    m_pCardIDLineEdit->SetLineEditText(sRunInfo.sCardInfo.strCardID);
}

void CRealFL::_ShowLast100FLID()
{
    QStringList strList = CHistoryDB::GetInstance()->getLastTestFLID(100);
    if(strList.size() > 0)
    {
        m_pCardIDComboBox->clear();
        m_pCardIDComboBox->addItems(strList);
        m_pCardIDComboBox->activated(strList.first());
    }
}

void CRealFL::_InitWidget()
{
    int iHeight = 35;

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, iHeight);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    m_pCardIDLineEdit = new CLabelLineEdit(tr("卡盒ID:"));
    m_pCardIDLineEdit->SetLineEditFixedSize(400, iHeight);
    connect(m_pCardIDLineEdit, &CLabelLineEdit::SignalTextChanged, this, &CRealFL::_SlotCardIDLineEditTextChanged);

    m_pCardIDComboBox = new QComboBox;
    m_pCardIDComboBox->setFixedSize(400, iHeight);
    m_pCardIDComboBox->setView(new QListView);
    m_pCardIDComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pCardIDComboBox->setMaxVisibleItems(10);
    m_pCardIDComboBox->setStyle(QStyleFactory::create("Windows"));
    connect(m_pCardIDComboBox, SIGNAL(activated(const QString &)), this, SLOT(_SlotCardIDComboBoxChanged(const QString &)));

    m_pStackedWidget = new QStackedWidget;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        CFLOneWidget *pFLWidget = new CFLOneWidget(i);
        connect(pFLWidget, &CFLOneWidget::SignalShowCurrentTestCardID, this, &CRealFL::_SlotShowCurrentTestCardID);
        m_pFLWidgetList.push_back(pFLWidget);
        m_pStackedWidget->addWidget(pFLWidget);
    }
}

void CRealFL::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->addSpacing(10);
    pTopLayout->setSpacing(10);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addSpacing(30);
    pTopLayout->addWidget(m_pCardIDLineEdit);
    pTopLayout->addWidget(m_pCardIDComboBox);
    pTopLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addSpacing(10);
    pLayout->addLayout(pTopLayout);
    pLayout->addWidget(m_pStackedWidget);
    this->setLayout(pLayout);
}
