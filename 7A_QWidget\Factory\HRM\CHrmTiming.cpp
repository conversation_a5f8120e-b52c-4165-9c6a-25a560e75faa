#include "CHrmTiming.h"
#include <QEvent>
#include <QDebug>
#include <QListView>
#include <QHeaderView>
#include <QJsonArray>
#include <QStyleFactory>
#include "CTecDB.h"
#include "CTimingTecDB.h"
#include "CMessageBox.h"

CHrmTiming::CHrmTiming(QWidget *parent)
    : QWidget(parent)
    , m_bShow(false)
    , m_iUiMachineID(0)
{
    Register2Map(Method_pcr_start);
    Register2Map(Method_pcr_stop);
    Register2Map(Method_pcr_tec_table_req);
    Register2Map(Method_pcr_tec_table_data);
    Register2Map(Method_pcr_tec_table_end);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        STecTimingStruct *pStruct = new STecTimingStruct;
        m_sTimingUiList.push_back(pStruct);
    }

    m_strCmdTextList << "TEC_SIN_RAMP_TO" << "TEC_LIN_RAMP_TO" << "TEC_HOLD_TIME"
                     << "TEC_REPEAT_MARK_START" << "TEC_REPEAT_MARK_END" << "TEC_HOLD_WITH_COUNTDOWN"
                     << "TEC_SET_PARAM" << "TEC_HRM_MARK_START" << "TEC_HRM_MARK_END" << "TEC_SIN_INCREASE_RAMP_TO" << "TEC_SIN_DECREASE_RAMP_TO"
                     << "TEC_HRM_CONTINUOUS_START" << "TEC_HRM_CONTINUOUS_END";

    this->setFixedSize(1494, 798);
    _InitWidget();

    _LoadName2Table();
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTecTestStart, this, &CHrmTiming::SlotSendTecByName);
}

CHrmTiming::~CHrmTiming()
{
    UnRegister2Map(Method_pcr_start);
    UnRegister2Map(Method_pcr_stop);
    UnRegister2Map(Method_pcr_tec_table_req);
    UnRegister2Map(Method_pcr_tec_table_data);
    UnRegister2Map(Method_pcr_tec_table_end);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        STecTimingStruct *pStruct = m_sTimingUiList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_sTimingUiList.clear();
}

void CHrmTiming::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(qVarData);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_pcr_start == iMethodID)
    {
        if(m_bShow)
        {
            if(0 == iResult)
                ShowSuccess(this, m_strTipsText, tr("%1#PCR启动成功").arg(iMachineID + 1));
            else
                ShowError(this, m_strTipsText, tr("%1#PCR启动失败").arg(iMachineID + 1));
        }
    }
    else if(Method_pcr_stop == iMethodID)
    {
        if(m_bShow)
        {
            if(0 == iResult)
                ShowSuccess(this, m_strTipsText, tr("%1#PCR停止成功").arg(iMachineID + 1));
            else
                ShowError(this, m_strTipsText, tr("%1#PCR停止失败").arg(iMachineID + 1));
        }
    }
    else if(Method_pcr_tec_table_req == iMethodID)
    {
        if(0 == iResult)
        {
            _SendTecData2LowMachine(iMachineID);
        }
        else
        {
            if(m_bShow)
                ShowError(this, m_strTipsText, tr("%1#下位机应答请求传输TEC时序表失败").arg(iMachineID + 1));
        }
    }
    else if(Method_pcr_tec_table_data == iMethodID)
    {
        if(0 == iResult)
        {
            _SendTecData2LowMachine(iMachineID);
        }
        else
        {
            if(m_bShow)
                ShowError(this, m_strTipsText, tr("%1#下位机应答传输TEC时序表数据失败").arg(iMachineID + 1));
        }
    }
    else if(Method_pcr_tec_table_end == iMethodID)
    {
        if(m_bShow)
        {
            if(0 == iResult)
                ShowSuccess(this, m_strTipsText, tr("%1#下位机应答传输TEC时序表结束成功").arg(iMachineID + 1));
            else
                ShowError(this, m_strTipsText, tr("%1#下位机应答传输TEC时序表结束失败").arg(iMachineID + 1));
        }
    }
}

void CHrmTiming::SlotSendTecByName(int iMachineID, const QString &strName)
{
    if(strName.isEmpty())
        return;

    QString strTecContent = CTimingTecDB::GetInstance().GetTecContent(strName);
    qDebug()<<QString("%1#下发tec:").arg(iMachineID + 1)<<strName<<strTecContent;
    if(strTecContent.isEmpty())
        return;

    _StartSendTec(iMachineID, strTecContent);
}

void CHrmTiming::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    QWidget::showEvent(pEvent);
}

void CHrmTiming::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

bool CHrmTiming::eventFilter(QObject *pObject, QEvent *pEvent)
{
    if(pObject->inherits("QComboBox"))
    {
        if(QEvent::Wheel == pEvent->type())
            return true;
    }

    return QWidget::eventFilter(pObject, pEvent);
}

void CHrmTiming::_SlotAddRowBtn()
{   
    QTableWidget *pTableWidget = m_sTimingUiList.at(m_iUiMachineID)->pCmdTable;
    int iRow = pTableWidget->currentRow();
    if(iRow < 0)
        iRow = -1;
    iRow++;

    pTableWidget->insertRow(iRow);
    _AddOneCmdLine(pTableWidget, iRow, {m_strCmdTextList.first(), "", ""});
    ResortTableWidget(pTableWidget);
    pTableWidget->selectRow(iRow);
}

void CHrmTiming::_SlotSubRowBtn()
{
    QTableWidget *pTableWidget = m_sTimingUiList.at(m_iUiMachineID)->pCmdTable;
    int iRow = pTableWidget->currentRow();
    if(iRow < 0)
        return;
    pTableWidget->removeRow(iRow);
    ResortTableWidget(pTableWidget);
}

void CHrmTiming::_SlotMachineChange(int iMachineID)
{
    m_iUiMachineID = iMachineID;
    m_pStackedWidget->setCurrentIndex(iMachineID);
}

void CHrmTiming::_SlotSaveBtn()
{
    QString strTecName = m_pSaveLineEdit->text();
    if(strTecName.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请输入要保存的时序名称"));
        return;
    }

    QStringList strList = _GetCmdTableDataList(m_iUiMachineID);
    if(strList.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("时序表格内容为空"));
        return;
    }
    QString strTecContent = strList.join(SPLIT_BETWEEN_CMD);
    qDebug()<<"保存tec时序:"<<strTecName<<strTecContent;
    CTimingTecDB::GetInstance().AddOneTec(strTecName, strTecContent);

    if(m_strTecNameList.contains(strTecName))
        return;

    m_strTecNameList.push_back(strTecName);
    emit CPublicConfig::GetInstance()->SignalUpdatePCRNameInfo(m_strTecNameList);

    int iRow = m_pFileTableWidget->rowCount();
    m_pFileTableWidget->insertRow(iRow);

    QTableWidgetItem *pIdItem = new QTableWidgetItem;
    pIdItem->setText(QString::number(iRow));
    pIdItem->setTextAlignment(Qt::AlignCenter);
    m_pFileTableWidget->setItem(iRow, 0, pIdItem);

    QTableWidgetItem *pNameItem = new QTableWidgetItem;
    pNameItem->setText(strTecName);
    pNameItem->setTextAlignment(Qt::AlignCenter);
    m_pFileTableWidget->setItem(iRow, 1, pNameItem);

    m_pFileTableWidget->selectRow(iRow);
}

void CHrmTiming::_SlotLoadBtn()
{
    int iRow = m_pFileTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择时序"));
        return;
    }

    _SlotClearBtn();

    QString strTecName = m_pFileTableWidget->item(iRow, 1)->text();
    if(strTecName.isEmpty())
        return;

    m_pLoadBtn->setEnabled(true);

    QString strTecContent = CTimingTecDB::GetInstance().GetTecContent(strTecName);
    if(strTecContent.isEmpty())
        return;
    QStringList strList = strTecContent.split(SPLIT_BETWEEN_CMD);
    int size = strList.size();
    qDebug()<<"tec加载时序:"<<size<<strTecName<<strTecContent;
    if(0 == size)
        return;

    QTableWidget *pWidget = m_sTimingUiList[m_iUiMachineID]->pCmdTable;
    pWidget->setRowCount(size + 5);
    for(int i=0; i<size; i++)
    {
        QStringList oneList = strList.at(i).split(SPLIT_IN_CMD);
        if(oneList.isEmpty())
            continue;

        _AddOneCmdLine(pWidget, i, oneList);
    }

    m_pLoadBtn->setEnabled(true);
}

void CHrmTiming::_SlotDeleteBtn()
{
    int iRow = m_pFileTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择时序"));
        return;
    }

    QString strTecName = m_pFileTableWidget->item(iRow, 1)->text();
    qDebug()<<"删除tec时序:"<<strTecName;
    m_strTecNameList.removeOne(strTecName);
    m_pFileTableWidget->removeRow(iRow);
    ResortTableWidget(m_pFileTableWidget);
    CTimingTecDB::GetInstance().DeleteOneTec(strTecName);
    emit CPublicConfig::GetInstance()->SignalUpdatePCRNameInfo(m_strTecNameList);
}

void CHrmTiming::_SlotClearBtn()
{
    _DeleteCmdTableWidget(m_iUiMachineID);
    m_sTimingUiList.at(m_iUiMachineID)->strTecDataList.clear();
    m_sTimingUiList.at(m_iUiMachineID)->pCmdTable->clearContents();
    m_sTimingUiList.at(m_iUiMachineID)->pCmdTable->setRowCount(0);
}

void CHrmTiming::_SlotSendBtn()
{
    QStringList strTecDataList = _GetCmdTableDataList(m_iUiMachineID);
    if(strTecDataList.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("时序表格内容为空"));
        return;
    }

    _StartSendTec(m_iUiMachineID, strTecDataList.join(SPLIT_BETWEEN_CMD));
}

void CHrmTiming::_SlotStartBtn()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定启动%1#PCR吗").arg(m_iUiMachineID + 1));
    if(QMessageBox::Yes != iBtnType)
        return;

    emit SignalStartPCR(m_iUiMachineID);
    QString strCmd = GetJsonCmdString(Method_pcr_start);
    SendJsonCmd(m_iUiMachineID, Method_pcr_start, strCmd);
}

void CHrmTiming::_SlotStopBtn()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定停止%1#PCR吗").arg(m_iUiMachineID + 1));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = GetJsonCmdString(Method_pcr_stop);
    SendJsonCmd(m_iUiMachineID, Method_pcr_stop, strCmd);
}

void CHrmTiming::_SlotComboBoxChanged(int index)
{
    Q_UNUSED(index);
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox)
        return;

    int iRow = pComboBox->property("row").toInt();
    CLineEdit *pLineEdit1 = new CLineEdit;
    pLineEdit1->setInputMethodHints(Qt::ImhDigitsOnly);
    m_sTimingUiList.at(m_iUiMachineID)->pCmdTable->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit1);

    CLineEdit *pLineEdit2 = new CLineEdit;
    pLineEdit2->setInputMethodHints(Qt::ImhDigitsOnly);
    m_sTimingUiList.at(m_iUiMachineID)->pCmdTable->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);
}

void CHrmTiming::_LoadName2Table()
{
    m_pFileTableWidget->clearContents();
    m_pFileTableWidget->setRowCount(0);

    m_strTecNameList = CTimingTecDB::GetInstance().GetTecNameList();
    if(m_strTecNameList.size() <= 0)
        return;

    m_pFileTableWidget->setRowCount(m_strTecNameList.size());
    for(int i=0; i<m_strTecNameList.size(); i++)
    {
        QTableWidgetItem* pIdItem = new QTableWidgetItem;
        pIdItem->setText(QString::number(i));
        pIdItem->setTextAlignment(Qt::AlignCenter);
        m_pFileTableWidget->setItem(i, 0, pIdItem);

        QTableWidgetItem* pNameItem = new QTableWidgetItem;
        pNameItem->setText(m_strTecNameList.at(i));
        pNameItem->setTextAlignment(Qt::AlignCenter);
        m_pFileTableWidget->setItem(i, 1, pNameItem);
    }
}

void CHrmTiming::_LoadData2Table(int iBegin, int iEnd)
{
    Q_UNUSED(iBegin);
    Q_UNUSED(iEnd);
}

void CHrmTiming::_AddOneCmdLine(QTableWidget *pTableWidget, int iRow, const QStringList &strOneList)
{
    if(strOneList.at(0).isEmpty())
        return;

    int first = m_strCmdTextList.indexOf(strOneList.at(0));
    QString second, third;
    if(strOneList.size() >= 2)
        second = strOneList.at(1);
    if(strOneList.size() >= 3)
        third = strOneList.at(2);

    QTableWidgetItem *pIdItem = new QTableWidgetItem;
    pIdItem->setText(QString::number(iRow));
    pIdItem->setTextAlignment(Qt::AlignCenter);
    pTableWidget->setItem(iRow, COL_NUM_ID, pIdItem);

    QComboBox *pComboBox = new QComboBox;
    pComboBox->setView(new QListView);
    pComboBox->setProperty("row", iRow);
    pComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    pComboBox->setMaxVisibleItems(10);
    pComboBox->setStyle(QStyleFactory::create("Windows"));
    pComboBox->addItems(m_strCmdTextList);
    pComboBox->setCurrentIndex(first);
    connect(pComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotComboBoxChanged(int)));
    pTableWidget->setCellWidget(iRow, COL_NUM_CMD, pComboBox);
    pComboBox->installEventFilter(this);

    CLineEdit *pLineEdit1 = new CLineEdit(second);
    pLineEdit1->setInputMethodHints(Qt::ImhDigitsOnly);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit1);

    CLineEdit *pLineEdit2 = new CLineEdit(third);
    pLineEdit2->setInputMethodHints(Qt::ImhDigitsOnly);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);
}

void CHrmTiming::_DeleteCmdTableWidget(int iMachineID)
{
    QTableWidget *pWidget = m_sTimingUiList.at(iMachineID)->pCmdTable;

    QList<QComboBox *> comboBoxList = pWidget->findChildren<QComboBox *>();
    foreach (QComboBox *pComboBox, comboBoxList)
    {
        disconnect(pComboBox, SIGNAL(activated(int)), this, SLOT(_SlotComboBoxChanged(int)));
        delete pComboBox;
        pComboBox = nullptr;
    }

    QList<CLineEdit *> lineEditList = pWidget->findChildren<CLineEdit *>();
    foreach (CLineEdit *pLineEdit, lineEditList)
    {
        delete pLineEdit;
        pLineEdit = nullptr;
    }

    qDebug()<<"delete tec cmd tablewidget:"<<comboBoxList.size()<<lineEditList.size();
}

QStringList CHrmTiming::_GetCmdTableDataList(int iMachineID)
{
    QStringList strDataList;
    QTableWidget *pTableWidget = m_sTimingUiList.at(iMachineID)->pCmdTable;
    int iCnt = pTableWidget->rowCount();
    for(int i=0; i<iCnt; i++)
    {
        QStringList oneList;
        QComboBox *pComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(i, COL_NUM_CMD));
        if(nullptr == pComboBox)
            continue;
        oneList.push_back(pComboBox->currentText());

        CLineEdit *pLineEdit1 = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(i, COL_NUM_PARAM1));
        if(nullptr != pLineEdit1)
            oneList.push_back(pLineEdit1->text().trimmed());
        CLineEdit *pLineEdit2 = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(i, COL_NUM_PARAM2));
        if(nullptr != pLineEdit2)
            oneList.push_back(pLineEdit2->text().trimmed());

        strDataList.push_back(oneList.join(SPLIT_IN_CMD));
    }
    return strDataList;
}

void CHrmTiming::_StartSendTec(int iMachineID, const QString &strTecData)
{
    //下发的是index+1,不是text
    QString strRawData = strTecData;
    for(int i=0; i<m_strCmdTextList.size(); i++)
        strRawData.replace(m_strCmdTextList.at(i), QString::number(i+1));

    QStringList strTecDataList = strRawData.split(SPLIT_BETWEEN_CMD);
    m_sTimingUiList[iMachineID]->strTecDataList = strTecDataList;

    int iLen = strTecDataList.length();
    int iPackets = iLen / 50;
    if(0 != iLen % 50)
        iPackets++;

    QVariantList qVarList = {iPackets, iLen};
    QString strCmd = GetJsonCmdString(Method_pcr_tec_table_req, qVarList);
    SendJsonCmd(iMachineID, Method_pcr_tec_table_req, strCmd);

    //for test
    //receiveMachineCmdReplay(m_iUiMachineID, Method_pcr_tec_table_req, true, QVariant());
}

static QVariantList _GetJsonVarList(const QStringList &strList)
{
    QVariantList qVarList;
    for(int i=0; i<strList.size(); i++)
    {
        QString str = strList.at(i);
        if(str.isEmpty())
            continue;

        QStringList oneList = str.split(SPLIT_IN_CMD);
        if(oneList.isEmpty())
            continue;

        QJsonArray array;
        array.append(oneList.at(0).toInt());
        if(oneList.size() >= 2)
        {
            if(!oneList.at(1).isEmpty())
                array.append(oneList.at(1).toInt());
        }
        if(oneList.size() >= 3)
        {
            if(!oneList.at(2).isEmpty())
                array.append(oneList.at(2).toInt());
        }

        qVarList.append(QVariant::fromValue(array));
    }
    return qVarList;
}

void CHrmTiming::_SendTecData2LowMachine(int iMachineID)
{
    QStringList &strDataList = m_sTimingUiList.at(iMachineID)->strTecDataList;
    int iLen = strDataList.length();
    if(0 == iLen)
    {
        QString strCmd = GetJsonCmdString(Method_pcr_tec_table_end);
        SendJsonCmd(iMachineID, Method_pcr_tec_table_end, strCmd);

        //for test
        //receiveMachineCmdReplay(iMachineID, Method_pcr_tec_table_end, true, QVariant());
    }
    else if(iLen <= 50)
    {
        QVariantList qVarList = _GetJsonVarList(strDataList);
        QString strCmd = GetJsonCmdString(Method_pcr_tec_table_data, qVarList);
        SendJsonCmd(iMachineID, Method_pcr_tec_table_data, strCmd);
        strDataList.clear();

        //for test
        //receiveMachineCmdReplay(iMachineID, Method_pcr_tec_table_data, true, QVariant());
    }
    else
    {
        QVariantList qVarList = _GetJsonVarList(strDataList.mid(0, 50));
        QString strCmd = GetJsonCmdString(Method_pcr_tec_table_data, qVarList);
        SendJsonCmd(iMachineID, Method_pcr_tec_table_data, strCmd);
        strDataList = strDataList.mid(50, iLen - 50);

        //for test
        //receiveMachineCmdReplay(iMachineID, Method_pcr_tec_table_data, true, QVariant());
    }
}

void CHrmTiming::_InitWidget()
{
    m_pAddRowBtn = new QPushButton(tr("增加一行"));
    m_pAddRowBtn->setFixedSize(130, 50);
    connect(m_pAddRowBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotAddRowBtn);

    m_pSubRowBtn = new QPushButton(tr("删除一行"));
    m_pSubRowBtn->setFixedSize(130, 50);
    connect(m_pSubRowBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotSubRowBtn);

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChange(int)));

    QHBoxLayout *pLeftBtnLayout = new QHBoxLayout;
    pLeftBtnLayout->setMargin(0);
    pLeftBtnLayout->addWidget(m_pAddRowBtn);
    pLeftBtnLayout->addSpacing(10);
    pLeftBtnLayout->addWidget(m_pSubRowBtn);
    pLeftBtnLayout->addStretch(1);
    pLeftBtnLayout->addWidget(m_pMachineComboBox);

    m_pStackedWidget = new QStackedWidget;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        QTableWidget *pWidget = _CreateCmdTableWidget();
        m_sTimingUiList[i]->pCmdTable = pWidget;
        m_pStackedWidget->addWidget(pWidget);
    }

    QVBoxLayout *pLeftLayout = new QVBoxLayout;
    pLeftLayout->setMargin(0);
    pLeftLayout->addWidget(m_pStackedWidget);
    pLeftLayout->addSpacing(10);
    pLeftLayout->addLayout(pLeftBtnLayout);

    //right
    m_pFileTableWidget = new QTableWidget;
    m_pFileTableWidget->setFixedWidth(275);
    m_pFileTableWidget->setRowCount(0);
    m_pFileTableWidget->setColumnCount(2);
    m_pFileTableWidget->setHorizontalHeaderLabels({tr("序号"), tr("时序")});

    QHeaderView* pVerticalHeader = m_pFileTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = m_pFileTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 70);
    pHorizontalHeader->resizeSection(1, 180);
    pHorizontalHeader->setStretchLastSection(true);

    m_pFileTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pFileTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pFileTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pFileTableWidget->setShowGrid(true);

    m_pSaveLineEdit = new CLineEdit;
    m_pSaveLineEdit->setPlaceholderText(tr("时序名称"));
    m_pSaveLineEdit->setFixedSize(130, 50);

    m_pSaveBtn = new QPushButton(tr("保存"));
    m_pSaveBtn->setFixedSize(130, 50);
    connect(m_pSaveBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotSaveBtn);

    m_pLoadBtn = new QPushButton(tr("加载"));
    m_pLoadBtn->setFixedSize(130, 50);
    connect(m_pLoadBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotLoadBtn);

    m_pDeleteBtn = new QPushButton(tr("删除"));
    m_pDeleteBtn->setFixedSize(130, 50);
    connect(m_pDeleteBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotDeleteBtn);

    m_pClearBtn = new QPushButton(tr("清空表格"));
    m_pClearBtn->setFixedSize(130, 50);
    connect(m_pClearBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotClearBtn);

    m_pSendBtn = new QPushButton(tr("下发时序"));
    m_pSendBtn->setFixedSize(130, 50);
    connect(m_pSendBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotSendBtn);

    m_pStartBtn = new QPushButton(tr("启动PCR"));
    m_pStartBtn->setFixedSize(130, 50);
    connect(m_pStartBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotStartBtn);

    m_pStopBtn = new QPushButton(tr("停止PCR"));
    m_pStopBtn->setFixedSize(130, 50);
    connect(m_pStopBtn, &QPushButton::clicked, this, &CHrmTiming::_SlotStopBtn);

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setVerticalSpacing(10);
    pGridLayout->setHorizontalSpacing(10);
    pGridLayout->addWidget(m_pSaveLineEdit, 0, 0);
    pGridLayout->addWidget(m_pSaveBtn, 0, 1);
    pGridLayout->addWidget(m_pLoadBtn, 1, 0);
    pGridLayout->addWidget(m_pDeleteBtn, 1, 1);
    pGridLayout->addWidget(m_pClearBtn, 2, 0);
    pGridLayout->addWidget(m_pSendBtn, 2, 1);
    pGridLayout->addWidget(m_pStartBtn, 3, 0);
    pGridLayout->addWidget(m_pStopBtn, 3, 1);

    QVBoxLayout *pRightLayout = new QVBoxLayout;
    pRightLayout->setMargin(0);
    pRightLayout->addWidget(m_pFileTableWidget);
    pRightLayout->addSpacing(10);
    pRightLayout->addLayout(pGridLayout);

    QHBoxLayout *pMainLayout = new QHBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addLayout(pLeftLayout);
    pMainLayout->addSpacing(10);
    pMainLayout->addLayout(pRightLayout);
    this->setLayout(pMainLayout);
}

QTableWidget *CHrmTiming::_CreateCmdTableWidget()
{
    QStringList strList;
    strList<<tr("序号")<<tr("命令")<<tr("参数1")<<tr("参数2");

    QTableWidget *pCmdWidget = new QTableWidget;
    pCmdWidget->setColumnCount(strList.size());
    pCmdWidget->setHorizontalHeaderLabels(strList);

    QHeaderView* pVerticalHeader = pCmdWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = pCmdWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 70);
    //pHorizontalHeader->resizeSection(1, 350);
    pHorizontalHeader->resizeSection(2, 130);
    pHorizontalHeader->resizeSection(3, 130);
    pHorizontalHeader->setSectionResizeMode(1, QHeaderView::Stretch);

    pCmdWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    pCmdWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    pCmdWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    pCmdWidget->setShowGrid(true);
    pCmdWidget->setFocusPolicy(Qt::NoFocus);

    QString strQSS = "QLineEdit{border-radius: 0px; border: 1px solid #CAD2DC;}"
                     "QComboBox{border-radius: 0px; border: 1px solid #CAD2DC;}";
    pCmdWidget->setStyleSheet(strQSS);

    return pCmdWidget;
}
