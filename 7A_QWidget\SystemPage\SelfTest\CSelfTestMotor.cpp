#include "CSelfTestMotor.h"

#include <QDebug>
#include "CRunTest.h"
#include "CTimingTecDB.h"

CSelfTestMotor::CSelfTestMotor(QObject *parent) : QObject(parent)
{
    Register2Map(Method_start);

    _CheckTimingExist();

    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_pInfoStructList.push_back(new SSelfTestInfoStruct());
    }
}

CSelfTestMotor::~CSelfTestMotor()
{
    UnRegister2Map(Method_start);

    for(int i=0; i<m_pInfoStructList.size(); i++)
    {
        SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_pInfoStructList.clear();
}

void CSelfTestMotor::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(qVarData);

    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(!pStruct->bStart)
        return;

    if(Method_start == iMethodID)
    {
        pStruct->bStart = false;
        if(0 != iResult)
        {
            emit CPublicConfig::GetInstance()->SignalSaveFaultCode(810, iMachineID);
        }
        emit SignalSelfTestResult(iMachineID, iResult);
    }
}

void CSelfTestMotor::StartSelfTest(int iMachineID)
{
    m_pInfoStructList.at(iMachineID)->bStart = true;

    QString strTimingData = CTimingTecDB::GetInstance().GetTimingContent(m_strTimingName);
    if(strTimingData.isEmpty())
        strTimingData = m_strTimingData;

    QDateTime dateTime = QDateTime::currentDateTime();
    QString strCurrentTime = dateTime.toString("yyyyMMddhhmmss");
    QString strCardID = "C" + strCurrentTime;
    QString strSampleID = "S" + strCurrentTime;
    QString strProject = "2019-nCoV/FluA/FluB/RSV";

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);
    sRunInfo.bFactroyTest = false;
    sRunInfo.iRunTimes = 1;
    sRunInfo.strTecName = "";
    sRunInfo.strTimingName = m_strTimingName;
    sRunInfo.strTimingData = strTimingData;
    sRunInfo.sCardInfo.strCardID = strCardID;
    sRunInfo.sCardInfo.strProject = strProject;
    sRunInfo.sSampleInfo.strSampleID = strSampleID;
    sRunInfo.sSampleInfo.strQCTestModel = "T";
    sRunInfo.sSampleInfo.strOperator = CPublicConfig::GetInstance()->GetLoginUser();
    qDebug()<<QString("%1#开始电机自检时序测试,时序:%2").arg(iMachineID + 1).arg(m_strTimingName);

    CRunTest::GetInstance()->StartTest(iMachineID);
}

void CSelfTestMotor::EndSelfTest(int iMachineID, bool bStop)
{
    qDebug()<<Q_FUNC_INFO<<iMachineID;
    if(iMachineID < 0 || iMachineID >= m_pInfoStructList.size())
        return;

    SSelfTestInfoStruct *pStruct = m_pInfoStructList.at(iMachineID);
    if(pStruct->bStart && bStop)
    {
        QString strCmd = GetJsonCmdString(Method_stop);
        qDebug()<<Q_FUNC_INFO<<iMachineID<<strCmd;
        SendJsonCmd(iMachineID, Method_stop, strCmd);
    }

    pStruct->Clear();
}

void CSelfTestMotor::_CheckTimingExist()
{
    m_strTimingName = "motorSelfTest";
    m_strTimingData = "10000,257;10000,313;10000,292;10000,332;10000,333;10000,262;10000,265;10000,280;"
                      "10000,281;10000,282;10000,283;10000,303;10000,295;10000,294;10000,305;10000,296;"
                      "10000,294;10000,304;10000,301;10000,298;10000,294;10000,315;10000,298;10000,294;"
                      "10000,306;10000,297;10000,294;10000,311;10000,310,5000;10000,312,2000;10000,309,5000;10000,258";

    if(!CTimingTecDB::GetInstance().IsTimingExist(m_strTimingName))
    {
        qDebug()<<"添加电机自检时序:"<<m_strTimingName<<m_strTimingData;
        CTimingTecDB::GetInstance().AddOneTiming(m_strTimingName, "", m_strTimingData);
    }
}
