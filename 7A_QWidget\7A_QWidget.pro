QT += core gui network serialport sql printsupport quickwidgets serialbus
QT += quickwidgets

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

TARGET = 7CAPP
TRANSLATIONS += 7CAPP-English.ts
#TRANSLATIONS += 7CAPP-German.ts
#TRANSLATIONS += 7CAPP-Italian.ts
#TRANSLATIONS += 7CAPP-Spanish.ts

include(QXlsx/QXlsx.pri)
include(MainVersion.pri)
include(WFKeyboard/keyboard.pri)

MOC_DIR = temp/moc
RCC_DIR = temp/rcc
UI_DIR = temp/ui
OBJECTS_DIR = temp/obj

#DESTDIR = C:\\Users\\<USER>\\Desktop\\7Crelease

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS
DEFINES += ZIP_STD

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    CConfigJson.cpp \
    CCreateImage.cpp \
    CHeartBeat.cpp \
    CMdxMainWidget.cpp \
    CRunTest.cpp \
    CmdBus/CCmdBase.cpp \
    CmdBus/CCmdManager.cpp \
    CommonWidget/CAgeWidget.cpp \
    CommonWidget/CBusyProgressBar.cpp \
    CommonWidget/CCheckUserWidget.cpp \
    CommonWidget/CDateTime.cpp \
    CommonWidget/CDateTimeWidget.cpp \
    CommonWidget/CHBtnTitleWidget.cpp \
    CommonWidget/CHLabelTitleWidget.cpp \
    CommonWidget/CIPLabelLineEdit.cpp \
    CommonWidget/CLabelCheckBox.cpp \
    CommonWidget/CLabelComboBox.cpp \
    CommonWidget/CLabelDate.cpp \
    CommonWidget/CLabelLabel.cpp \
    CommonWidget/CLabelLineEdit.cpp \
    CommonWidget/CLineEdit.cpp \
    CommonWidget/CLineEditSpinBox.cpp \
    CommonWidget/CLineTwoEdit.cpp \
    CommonWidget/CMessageBox.cpp \
    CommonWidget/CNewLabelDate.cpp \
    CommonWidget/CPressLabel.cpp \
    CommonWidget/CProgressBar.cpp \
    CommonWidget/CRangeLineEdit.cpp \
    CommonWidget/CSetChartXYRange.cpp \
    CommonWidget/CTextBrowser.cpp \
    CommonWidget/CVBtnTitleWidget.cpp \
    CommonWidget/CVIndexTextLabel.cpp \
    CommonWidget/qcustomplot.cpp \
    DBControl/CDevInfoDB.cpp \
    DBControl/CFtpDB.cpp \
    DBControl/CHistoryDB.cpp \
    DBControl/CLogDB.cpp \
    DBControl/CLotInfoDB.cpp \
    DBControl/CMotorDB.cpp \
    DBControl/CMotorInfoDB.cpp \
    DBControl/CProjectDB.cpp \
    DBControl/CRegisterDB.cpp \
    DBControl/CSqliteDBBase.cpp \
    DBControl/CSystemDB.cpp \
    DBControl/CTecDB.cpp \
    DBControl/CTimingTecDB.cpp \
    DBControl/CUserDB.cpp \
    Factory/CMachineDebug.cpp \
    Factory/CMeltingCurve.cpp \
    Factory/CMotorCompose.cpp \
    Factory/CMotorDebug.cpp \
    Factory/CRealFL.cpp \
    Factory/CRunEnvWidget.cpp \
    Factory/CSetSerialPort.cpp \
    Factory/CTimingCompose.cpp \
    Factory/DataManager/CDataExport.cpp \
    Factory/DataManager/CDataManager.cpp \
    Factory/DataManager/CFaultCodeManager.cpp \
    Factory/DataManager/CFaultLog.cpp \
    Factory/DataManager/CPressureInfo.cpp \
    Factory/DataManager/CPressureOneCurve.cpp \
    Factory/DataManager/CProjectManager.cpp \
    Factory/DataManager/CModifyParam.cpp \
    Factory/DataManager/CRunLog.cpp \
    Factory/DataManager/CSelfTestParams.cpp \
    Factory/DataManager/CShowLogDetail.cpp \
    Factory/HRM/CHrmCalibrate.cpp \
    Factory/HRM/CHrmCurve.cpp \
    Factory/HRM/CHrmOneCalibrate.cpp \
    Factory/HRM/CHrmTiming.cpp \
    Factory/HRM/CHrmWidget.cpp \
    Factory/LightDebug/CLightCalibrate.cpp \
    Factory/LightDebug/CLightCurve.cpp \
    Factory/LightDebug/CLightDebug.cpp \
    Factory/LightDebug/CLightMDT.cpp \
    Factory/LightDebug/CLightOneCurve.cpp \
    Factory/LightDebug/CLightOneMDT.cpp \
    Factory/LightDebug/CLightSingleCmd.cpp \
    Factory/LightDebug/COpticalCalibration.cpp \
    Factory/LightDebug/CFluorescenceInterference.cpp \
    Factory/LightDebug/CPerformanceTest.cpp \
    Factory/LightDebug/CNoiseTest.cpp \
    Factory/LightDebug/CLightOneTiming.cpp \
    Factory/MachineDebug/CMachineCmd.cpp \
    Factory/MachineDebug/CMachineLogConfig.cpp \
    Factory/MachineDebug/CMedianUS.cpp \
    Factory/MachineDebug/CSoftUpdate.cpp \
    Factory/MachineDebug/CUpdateRecordWidget.cpp \
    Factory/Melting/CMeltingOneWidget.cpp \
    Factory/MotorCompose/COptoCompose.cpp \
    Factory/MotorCompose/COptoCountTouchStop.cpp \
    Factory/MotorCompose/CSixParams.cpp \
    Factory/MotorCompose/CThreeParams.cpp \
    Factory/MotorDebug/CMotorCalibrate.cpp \
    Factory/MotorDebug/CMotorChopper.cpp \
    Factory/MotorDebug/CMotorConfig.cpp \
    Factory/MotorDebug/CMotorCopmensate.cpp \
    Factory/MotorDebug/CMotorGXIO.cpp \
    Factory/MotorDebug/CMotorMethod.cpp \
    Factory/MotorDebug/CMotorPosition.cpp \
    Factory/MotorDebug/CMotorRegister.cpp \
    Factory/MotorDebug/CNormalMotor.cpp \
    Factory/Pyrolysis/CPyrolysis.cpp \
    Factory/Pyrolysis/CPyrolysisCalibration.cpp \
    Factory/Pyrolysis/CPyrolysisCurve.cpp \
    Factory/Pyrolysis/CPyrolysisDebug.cpp \
    Factory/RealFL/CFLOneWidget.cpp \
    Factory/RealFL/CFLSetParamWidget.cpp \
    Factory/Settings/CSettingsWidget.cpp \
    Factory/TimingCompose/CMotorSerialCompose.cpp \
    HistoryPage/CHistoryDetailAmpCurve.cpp \
    HistoryPage/CHistoryDetailHrmCurve.cpp \
    HistoryPage/CHistoryDetailWidget.cpp \
    HistoryPage/CHistoryItemWidget.cpp \
    HistoryPage/CHistoryPage.cpp \
    HistoryPage/CHistorySearchWidget.cpp \
    HistoryPage/CManualHrmReviewWidget.cpp \
    HistoryPage/CManualReviewWidget.cpp \
    HomePage/CHomeDevGroupWidget.cpp \
    HomePage/CHomeDevItemWidget.cpp \
    HomePage/CHomeDeviceWidget.cpp \
    HomePage/CHomeEnterInfoWidget.cpp \
    HomePage/CHomePage.cpp \
    HomePage/CHomeSelectProjectWidget.cpp \
    Log/CMylog.cpp \
    LoginPage/CLoginWidget.cpp \
    LoginPage/CPasswordLineEdit.cpp \
    LoginPage/CUserNameComboBox.cpp \
    LoginPage/algorithmdescrypt.cpp \
    MDControl/CCalculateThread.cpp \
    MDControl/CCanBusThread.cpp \
    MDControl/CHL7MsgThread.cpp \
    MDControl/CLisTcpClient.cpp \
    MDControl/CPdfTcpServer.cpp \
    MDControl/CPrintThread.cpp \
    MDControl/CScanCodeThread.cpp \
    PowerPage/CLockScreenPage.cpp \
    PowerPage/CPowerPage.cpp \
    PublicConfig.cpp \
    PublicFunction.cpp \
    Keyboard/CQmlKeyboard.cpp \
    Log/CMyLogThread.cpp \
    MDControl/CFirmLogSerialThread.cpp \
    MDControl/COperationUnit.cpp \
    MDControl/CPdfHelper.cpp \
    MDControl/CReadWriteXlsxThread.cpp \
    MDControl/CSerialThread.cpp \
   # TestWidget.cpp \
    QCPage/CQCDevGroupWidget.cpp \
    QCPage/CQCDevItemBtn.cpp \
    QCPage/CQCPage.cpp \
    SystemPage/CSysButton.cpp \
    SystemPage/CSysFirstTitleWidget.cpp \
    SystemPage/CSysSecondTitleWidget.cpp \
    SystemPage/CSystemMainWidget.cpp \
    SystemPage/CSystemPage.cpp \
    SystemPage/Calibration/CCaliDevGroupWidget.cpp \
    SystemPage/Calibration/CCaliDevItemWidget.cpp \
    SystemPage/Calibration/CCaliDeviceWidget.cpp \
    SystemPage/Calibration/CCaliHistoryWidget.cpp \
    SystemPage/Calibration/CCalibrationWidget.cpp \
    SystemPage/DeviceInfo/CDeviceInfoWidget.cpp \
    SystemPage/DeviceInfo/CIPCInfoWidget.cpp \
    SystemPage/DeviceInfo/CPLCDetailWidget.cpp \
    SystemPage/DeviceInfo/CPLCDevGroupWidget.cpp \
    SystemPage/DeviceInfo/CPLCDevItemWidget.cpp \
    SystemPage/DeviceInfo/CPLCInfoWidget.cpp \
    SystemPage/Log/CFaultLogWidget.cpp \
    SystemPage/Log/CLogWidget.cpp \
    SystemPage/Log/COperationLogWidget.cpp \
    SystemPage/Log/CSystemLogWidget.cpp \
    SystemPage/Maintian/CMaintainWidget.cpp \
    SystemPage/MyFactory/CFactoryWidget.cpp \
    SystemPage/General/CGeneralWidget.cpp \
    #SystemPage/Network/CLocalNetworkHandle.cpp \
    SystemPage/Network/CNetworkWidget.cpp \
    SystemPage/Network/Eth1/CEth1Widget.cpp \
    SystemPage/Network/Lis/CLisWidget.cpp \
    SystemPage/Network/WiFi/CWiFiInputWidget.cpp \
    SystemPage/Network/WiFi/CWiFiItemWidget.cpp \
    SystemPage/Network/WiFi/CWiFiThread.cpp \
    SystemPage/Network/WiFi/CWiFiWidget.cpp \
    SystemPage/Printer/CPrinterWidget.cpp \
    SystemPage/SelfTest/CSelfTestDevItemWidget.cpp \
    SystemPage/SelfTest/CSelfTestDeviceWidget.cpp \
    SystemPage/SelfTest/CSelfTestHistoryWidget.cpp \
    SystemPage/SelfTest/CSelfTestLysis.cpp \
    SystemPage/SelfTest/CSelfTestMotor.cpp \
    SystemPage/SelfTest/CSelfTestPressure.cpp \
    SystemPage/SelfTest/CSelfTestTEC.cpp \
    SystemPage/SelfTest/CSelfTestWidget.cpp \
    SystemPage/Update/CUpdateResultPage.cpp \
    SystemPage/Update/CUpdateWidget.cpp \
    SystemPage/Update/QAes/qaesencryption.cpp \
    SystemPage/User/CAddEditUserWidget.cpp \
    SystemPage/User/CUserWidget.cpp \
    WFKeyboard/wfkeyboard.cpp \
    ZipManager/unzip.cpp \
    ZipManager/zip.cpp \
    main.cpp \
    MainWindow.cpp \
    sparamfun.cpp

HEADERS += \
    CConfigJson.h \
    CCreateImage.h \
    CHeartBeat.h \
    CMdxMainWidget.h \
    CRunTest.h \
    CmdBus/CCmdBase.h \
    CmdBus/CCmdManager.h \
    CommonWidget/CAgeWidget.h \
    CommonWidget/CBusyProgressBar.h \
    CommonWidget/CCheckUserWidget.h \
    CommonWidget/CDateTime.h \
    CommonWidget/CDateTimeWidget.h \
    CommonWidget/CHBtnTitleWidget.h \
    CommonWidget/CHLabelTitleWidget.h \
    CommonWidget/CIPLabelLineEdit.h \
    CommonWidget/CLabelCheckBox.h \
    CommonWidget/CLabelComboBox.h \
    CommonWidget/CLabelDate.h \
    CommonWidget/CLabelLabel.h \
    CommonWidget/CLabelLineEdit.h \
    CommonWidget/CLineEdit.h \
    CommonWidget/CLineEditSpinBox.h \
    CommonWidget/CLineTwoEdit.h \
    CommonWidget/CMessageBox.h \
    CommonWidget/CNewLabelDate.h \
    CommonWidget/CPressLabel.h \
    CommonWidget/CProgressBar.h \
    CommonWidget/CRangeLineEdit.h \
    CommonWidget/CSetChartXYRange.h \
    CommonWidget/CTextBrowser.h \
    CommonWidget/CVBtnTitleWidget.h \
    CommonWidget/CVIndexTextLabel.h \
    CommonWidget/qcustomplot.h \
    DBControl/CDevInfoDB.h \
    DBControl/CFtpDB.h \
    DBControl/CHistoryDB.h \
    DBControl/CLogDB.h \
    DBControl/CLotInfoDB.h \
    DBControl/CMotorDB.h \
    DBControl/CMotorInfoDB.h \
    DBControl/CProjectDB.h \
    DBControl/CRegisterDB.h \
    DBControl/CSqliteDBBase.h \
    DBControl/CSystemDB.h \
    DBControl/CTecDB.h \
    DBControl/CTimingTecDB.h \
    DBControl/CUserDB.h \
    Factory/CMachineDebug.h \
    Factory/CMeltingCurve.h \
    Factory/CMotorCompose.h \
    Factory/CMotorDebug.h \
    Factory/CRealFL.h \
    Factory/CRunEnvWidget.h \
    Factory/CSetSerialPort.h \
    Factory/CTimingCompose.h \
    Factory/DataManager/CDataExport.h \
    Factory/DataManager/CDataManager.h \
    Factory/DataManager/CFaultCodeManager.h \
    Factory/DataManager/CFaultLog.h \
    Factory/DataManager/CPressureInfo.h \
    Factory/DataManager/CPressureOneCurve.h \
    Factory/DataManager/CProjectManager.h \
    Factory/DataManager/CModifyParam.h \
    Factory/DataManager/CRunLog.h \
    Factory/DataManager/CSelfTestParams.h \
    Factory/DataManager/CShowLogDetail.h \
    Factory/HRM/CHrmCalibrate.h \
    Factory/HRM/CHrmCurve.h \
    Factory/HRM/CHrmOneCalibrate.h \
    Factory/HRM/CHrmTiming.h \
    Factory/HRM/CHrmWidget.h \
    Factory/LightDebug/CLightCalibrate.h \
    Factory/LightDebug/CLightCurve.h \
    Factory/LightDebug/CLightDebug.h \
    Factory/LightDebug/CLightMDT.h \
    Factory/LightDebug/CLightOneCurve.h \
    Factory/LightDebug/CLightOneMDT.h \
    Factory/LightDebug/CLightSingleCmd.h \
    Factory/LightDebug/COpticalCalibration.h \
    Factory/LightDebug/CFluorescenceInterference.h \
    Factory/LightDebug/CPerformanceTest.h \
    Factory/LightDebug/CNoiseTest.h \
    Factory/LightDebug/CLightOneTiming.h \
    Factory/MachineDebug/CMachineCmd.h \
    Factory/MachineDebug/CMachineLogConfig.h \
    Factory/MachineDebug/CMedianUS.h \
    Factory/MachineDebug/CSoftUpdate.h \
    Factory/MachineDebug/CUpdateRecordWidget.h \
    Factory/Melting/CMeltingOneWidget.h \
    Factory/MotorCompose/COptoCompose.h \
    Factory/MotorCompose/COptoCountTouchStop.h \
    Factory/MotorCompose/CSixParams.h \
    Factory/MotorCompose/CThreeParams.h \
    Factory/MotorDebug/CMotorCalibrate.h \
    Factory/MotorDebug/CMotorChopper.h \
    Factory/MotorDebug/CMotorConfig.h \
    Factory/MotorDebug/CMotorCopmensate.h \
    Factory/MotorDebug/CMotorGXIO.h \
    Factory/MotorDebug/CMotorMethod.h \
    Factory/MotorDebug/CMotorPosition.h \
    Factory/MotorDebug/CMotorRegister.h \
    Factory/MotorDebug/CNormalMotor.h \
    Factory/Pyrolysis/CPyrolysis.h \
    Factory/Pyrolysis/CPyrolysisCalibration.h \
    Factory/Pyrolysis/CPyrolysisCurve.h \
    Factory/Pyrolysis/CPyrolysisDebug.h \
    Factory/RealFL/CFLOneWidget.h \
    Factory/RealFL/CFLSetParamWidget.h \
    Factory/Settings/CSettingsWidget.h \
    Factory/TimingCompose/CMotorSerialCompose.h \
    HistoryPage/CHistoryDetailAmpCurve.h \
    HistoryPage/CHistoryDetailHrmCurve.h \
    HistoryPage/CHistoryDetailWidget.h \
    HistoryPage/CHistoryItemWidget.h \
    HistoryPage/CHistoryPage.h \
    HistoryPage/CHistorySearchWidget.h \
    HistoryPage/CManualHrmReviewWidget.h \
    HistoryPage/CManualReviewWidget.h \
    HomePage/CHomeDevGroupWidget.h \
    HomePage/CHomeDevItemWidget.h \
    HomePage/CHomeDeviceWidget.h \
    HomePage/CHomeEnterInfoWidget.h \
    HomePage/CHomePage.h \
    HomePage/CHomeSelectProjectWidget.h \
    Log/CMylog.h \
    LoginPage/CLoginWidget.h \
    LoginPage/CPasswordLineEdit.h \
    LoginPage/CUserNameComboBox.h \
    LoginPage/algorithmdescrypt.h \
    MDControl/CCalculateThread.h \
    MDControl/CCanBusThread.h \
    MDControl/CHL7MsgThread.h \
    MDControl/CLisTcpClient.h \
    MDControl/CPdfTcpServer.h \
    MDControl/CPrintThread.h \
    MDControl/CScanCodeThread.h \
    PowerPage/CLockScreenPage.h \
    PowerPage/CPowerPage.h \
    PublicConfig.h \
    PublicFunction.h \
    PublicParams.h \
    Keyboard/CQmlKeyboard.h \
    Log/CMyLogThread.h \
    MDControl/CFirmLogSerialThread.h \
    MDControl/COperationUnit.h \
    MDControl/CPdfHelper.h \
    MDControl/CReadWriteXlsxThread.h \
    MDControl/CSerialThread.h \
    MainWindow.h \
  #  TestWidget.h \
    QCPage/CQCDevGroupWidget.h \
    QCPage/CQCDevItemBtn.h \
    QCPage/CQCPage.h \
    SystemPage/CSysButton.h \
    SystemPage/CSysFirstTitleWidget.h \
    SystemPage/CSysSecondTitleWidget.h \
    SystemPage/CSystemMainWidget.h \
    SystemPage/CSystemPage.h \
    SystemPage/Calibration/CCaliDevGroupWidget.h \
    SystemPage/Calibration/CCaliDevItemWidget.h \
    SystemPage/Calibration/CCaliDeviceWidget.h \
    SystemPage/Calibration/CCaliHistoryWidget.h \
    SystemPage/Calibration/CCalibrationWidget.h \
    SystemPage/DeviceInfo/CDeviceInfoWidget.h \
    SystemPage/DeviceInfo/CIPCInfoWidget.h \
    SystemPage/DeviceInfo/CPLCDetailWidget.h \
    SystemPage/DeviceInfo/CPLCDevGroupWidget.h \
    SystemPage/DeviceInfo/CPLCDevItemWidget.h \
    SystemPage/DeviceInfo/CPLCInfoWidget.h \
    SystemPage/Log/CFaultLogWidget.h \
    SystemPage/Log/CLogWidget.h \
    SystemPage/Log/COperationLogWidget.h \
    SystemPage/Log/CSystemLogWidget.h \
    SystemPage/Maintian/CMaintainWidget.h \
    SystemPage/MyFactory/CFactoryWidget.h \
    SystemPage/General/CGeneralWidget.h \
    #SystemPage/Network/CLocalNetworkHandle.h \
    SystemPage/Network/CNetworkWidget.h \
    SystemPage/Network/Eth1/CEth1Widget.h \
    SystemPage/Network/Lis/CLisWidget.h \
    SystemPage/Network/WiFi/CWiFiInputWidget.h \
    SystemPage/Network/WiFi/CWiFiItemWidget.h \
    SystemPage/Network/WiFi/CWiFiThread.h \
    SystemPage/Network/WiFi/CWiFiWidget.h \
    SystemPage/Printer/CPrinterWidget.h \
    SystemPage/SelfTest/CSelfTestDevItemWidget.h \
    SystemPage/SelfTest/CSelfTestDeviceWidget.h \
    SystemPage/SelfTest/CSelfTestHistoryWidget.h \
    SystemPage/SelfTest/CSelfTestLysis.h \
    SystemPage/SelfTest/CSelfTestMotor.h \
    SystemPage/SelfTest/CSelfTestPressure.h \
    SystemPage/SelfTest/CSelfTestTEC.h \
    SystemPage/SelfTest/CSelfTestWidget.h \
    SystemPage/Update/CUpdateResultPage.h \
    SystemPage/Update/CUpdateWidget.h \
    SystemPage/Update/QAes/qaesencryption.h \
    SystemPage/User/CAddEditUserWidget.h \
    SystemPage/User/CUserWidget.h \
    WFKeyboard/wfkeyboard.h \
    ZipManager/unzip.h \
    ZipManager/zip.h \
    include/ccalctlib.h \
    include/cmeltingcalclib.h \
    include/cpniresultlib.h

FORMS += \
    MainWindow.ui \
    Widget.ui

win32 {
      LIBS += -lDbgHelp
      LIBS += $$PWD/lib/windows/libCCalCTLib.a
      LIBS += $$PWD/lib/windows/HL7/libHL7Lib.a
}
unix {
     contains(QT_ARCH, arm64){
       LIBS += -L$$PWD/lib/arm/ -lCCalCTLib
       LIBS += $$PWD/lib/arm/libcrypto.a
       LIBS += $$PWD/lib/arm/libssl.a
	   LIBS += -L$$PWD/lib/arm/ -lHL7Lib
     } else {
       LIBS += -L$$PWD/lib/x64/ -lCCalCTLib
       LIBS += $$PWD/lib/x64/libcrypto.a
       LIBS += $$PWD/lib/x64/libssl.a
	   LIBS += -L$$PWD/lib/x64/ -lHL7Lib
     }
}

INCLUDEPATH += $$PWD/GlobalBase/   \
            += $$PWD/CommonWidget/ \
            += $$PWD/Keyboard/ \
            += $$PWD/Log/ \
            += $$PWD/DBControl/ \
            += $$PWD/CmdBus/ \
            += $$PWD/MDControl/ \
            += $$PWD/Factory/ \
            += $$PWD/include/

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    ResourceFile.qrc
