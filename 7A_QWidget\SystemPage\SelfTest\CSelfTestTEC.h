#ifndef CSELFTESTTEC_H
#define CSELFTESTTEC_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-07-10
  * Description: 温控自检
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include <QDateTime>
#include "CCmdBase.h"
#include "PublicParams.h"

class CSelfTestTEC : public QObject , public CCmdBase
{
    Q_OBJECT
public:
    explicit CSelfTestTEC(QObject *parent = nullptr);
    ~CSelfTestTEC();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

    void StartSelfTest(int iMachineID, QString strXlsxPath);
    void EndSelfTest(int iMachineID);

signals:
    void SignalSelfTestResult(int iMachineID, int iResult);

private:
    void _CheckTECExist();
    void _HandlePCRInfo(int iMachineID, const QVariant &qVarData);
    void _HandlePCRSignal(int iMachineID, const QVariant &qVarData);
    void _CheckTempData(int iMachineID, int index);
    void _WriteXlsx(int iMachineID);

private:
    struct SSelfTestInfoStruct
    {
        SSelfTestInfoStruct()
        {
            bStart = false;
            iResult1 = -1;
            iResult2 = -1;
        }

        void Clear()
        {
            bStart = false;
            iResult1 = -1;
            iResult2 = -1;
            strXlsxPath.clear();
            strDetails.clear();
            dTemp1Vec.clear();
            dTemp2Vec.clear();
            dTimeVec.clear();
        }

        bool bStart;               //是否启动自检
        int iResult1;              //模块1自检结果
        int iResult2;              //模块2自检结果
        QString strXlsxPath;       //内部自检结果表
        QString strDetails;        //详情
        QDateTime qBeginTime;      //起始时间
        QVector<double> dTemp1Vec; //模块1温度
        QVector<double> dTemp2Vec; //模块2温度
        QVector<double> dTimeVec;  //时间
    };

private:
    QString m_strTECName;
    QString m_strTecData;
    STECSelfTestParamsStruct m_sParamsStruct;
    QList<SSelfTestInfoStruct *> m_pInfoStructList;
};

#endif // CSELFTESTTEC_H
