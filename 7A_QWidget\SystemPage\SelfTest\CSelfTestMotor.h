#ifndef CSELFTESTMOTOR_H
#define CSELFTESTMOTOR_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-07-09
  * Description: 电机自检
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CCmdBase.h"

class CSelfTestMotor : public QObject , public CCmdBase
{
    Q_OBJECT
public:
    explicit CSelfTestMotor(QObject *parent = nullptr);
    ~CSelfTestMotor();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

    void StartSelfTest(int iMachineID);
    void EndSelfTest(int iMachineID, bool bStop);

signals:
    void SignalSelfTestResult(int iMachineID, int iResult);

private:
    void _CheckTimingExist();

private:
    struct SSelfTestInfoStruct
    {
        SSelfTestInfoStruct()
        {
            bStart = false;
        }

        void Clear()
        {
            bStart = false;
        }

        bool bStart;
    };

private:
    QString m_strTimingName;
    QString m_strTimingData;
    QList<SSelfTestInfoStruct *> m_pInfoStructList;
};

#endif // CSELFTESTMOTOR_H
