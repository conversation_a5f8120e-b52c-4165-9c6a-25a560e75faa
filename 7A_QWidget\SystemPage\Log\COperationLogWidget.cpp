#include "COperationLogWidget.h"
#include <QListView>
#include <QBoxLayout>
#include <QHeaderView>
#include <QStyleFactory>
#include <QElapsedTimer>

#include "CLogDB.h"
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "Factory/DataManager/CShowLogDetail.h"
#include "CReadWriteXlsxThread.h"

COperationLogWidget::COperationLogWidget(QWidget *parent) : QWidget(parent)
{
    this->setFixedSize(1636, 800);
    _InitWidget();
    _InitLayout();

    m_bRefresh = false;
    m_bShow = false;
    m_iOnePageLines = 12;
    m_strTipsText = tr("提示");

    m_pExportBar = new CBusyProgressBar(m_strTipsText, tr("正在导出操作日志"), this);
    m_pExportBar->setVisible(false);

    connect(this, &COperationLogWidget::SignalRefreshEnd, this, &COperationLogWidget::_SlotRefreshEnd);
    connect(&CLogDB::instance(), &CLogDB::SignalRefreshOperationLog, this, &COperationLogWidget::SlotRefreshLog);
}

void COperationLogWidget::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    QWidget::showEvent(pEvent);
}

void COperationLogWidget::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void COperationLogWidget::SlotRefreshLog()
{
    _SlotShowAllBtn();
}

void COperationLogWidget::_SlotGotoPageBtn()
{
    QString strPage = m_pGotoLineEdit->text();
    if(strPage.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请输入要跳转到的页数"));
        return;
    }

    int iPage = strPage.toInt();
    if(iPage <= 0)
        iPage = 1;
    if(iPage >= m_iTotalPages)
        iPage = m_iTotalPages;
    m_pGotoLineEdit->setText(QString::number(iPage));

    m_iCurrentPage = iPage - 1;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();
}

void COperationLogWidget::_SlotPrePageBtn()
{
    if(m_iCurrentPage <= 0)
        return;

    m_iCurrentPage--;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();
}

void COperationLogWidget::_SlotNextPageBtn()
{
    if(m_iCurrentPage + 1 >= m_iTotalPages)
        return;

    m_iCurrentPage++;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();
}

void COperationLogWidget::_SlotQueryBtn()
{
    QString strQuery = m_pQueryLineEdit->text().trimmed();
    if(strQuery.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请输入要查询的内容"));
        return;
    }

    int index = m_pQueryComboBox->currentIndex();
    m_bHasQuery = true;
    m_iCurrentPage = 0;
    m_strQueryLogList.clear();
    for(int i=0; i<m_strAllLogList.size(); i++)
    {
        QStringList strOneList = m_strAllLogList.at(i);
        if(0 == index || 1 == index)
        {
            if(strQuery == strOneList.at(index))
            {
                m_strQueryLogList.push_back(strOneList);
            }
        }
        else
        {
            if(strOneList.at(index).contains(strQuery, Qt::CaseInsensitive))
            {
                m_strQueryLogList.push_back(strOneList);
            }
        }
    }

    m_iTotalLines = m_strQueryLogList.size();
    m_iTotalPages = m_iTotalLines / m_iOnePageLines;
    m_iLeftLines = m_iTotalLines % m_iOnePageLines;
    if(0 != m_iLeftLines)
        m_iTotalPages++;
    qDebug()<<"操作日志查询内容:"<<strQuery
           <<",查询结果总数:"<<m_iTotalLines<<",页数:"<<m_iTotalPages
          <<",当前页数:"<<m_iCurrentPage<<",每页数量:"<<m_iOnePageLines;
    _ShowCurrentPageQueryData();
}

void COperationLogWidget::_SlotShowAllBtn()
{
    //防止连点
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(m_iOnePageLines);

    m_pExportBar->ResetInfoText(tr("正在刷新"));
    m_pExportBar->setVisible(true);
    if(m_bShow)
        Delay_MSec(1000);

    std::thread mythread(&COperationLogWidget::_Thread2Refresh, this);
    mythread.detach();
}

void COperationLogWidget::_Thread2Refresh()
{
    m_bHasQuery = false;
    m_iCurrentPage = 0;

    m_strAllLogList.clear();
    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    bool bReview = CPublicConfig::GetInstance()->GetShowRawCurve();
    CLogDB::instance().ReadAllOperationLog(strUser, bReview, m_strAllLogList);
    int size = m_strAllLogList.size();
    for(int i=0; i<size; i++)
    {
        QStringList &strOneList = m_strAllLogList[i];
        strOneList[0] = QString::number(size - i);
    }

    m_iTotalLines = m_strAllLogList.size();
    m_iTotalPages = m_iTotalLines / m_iOnePageLines;
    m_iLeftLines = m_iTotalLines % m_iOnePageLines;
    if(0 != m_iLeftLines)
        m_iTotalPages++;
    qDebug()<<"操作日志总数:"<<m_iTotalLines<<",页数:"<<m_iTotalPages
           <<",当前页数:"<<m_iCurrentPage<<",每页数量:"<<m_iOnePageLines;

    emit SignalRefreshEnd();
}

void COperationLogWidget::_SlotRefreshEnd()
{
    _ShowCurrentPageAllData();
    m_pExportBar->setVisible(false);
}

void COperationLogWidget::_SlotDetailBtn()
{
    int iRow = m_pTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择一行"));
        return;
    }

    QStringList strDataList;
    for(int i=0; i<m_strTitleList.size(); i++)
    {
        QTableWidgetItem *pItem = m_pTableWidget->item(iRow, i);
        if(pItem)
        {
            QString strOneLine = QString("%1: %2").arg(m_strTitleList.at(i)).arg(pItem->text());
            strDataList.push_back(strOneLine);
        }
    }
    CShowLogDetail *pShow = new CShowLogDetail(this);
    pShow->ShowTextList(strDataList);
}

void COperationLogWidget::_SlotExportBtn()
{
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    if(!UDiskExistAndCreateDir(strExportDir, this))
        return;

    m_pExportBar->ResetInfoText(tr("正在导出"));
    m_pExportBar->setVisible(true);

    std::thread mythread(&COperationLogWidget::_Thread2Export, this);
    mythread.detach();
}

void COperationLogWidget::_Thread2Export()
{
    qDebug()<<Q_FUNC_INFO<<QThread::currentThreadId();
    QString strSN = CPublicConfig::GetInstance()->GetMachineSN();
    strSN = DeleteSpecialCharacters(strSN);
    QString strTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strXlsxName = QString("operationlog_%1_%2.xlsx").arg(strSN).arg(strTime);

    STXlsxParmasStruct* pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "operationlog";
    pXlsxStruct->strTitleList = m_strTitleList;

    QList<QStringList> strAllList = m_strAllLogList;

    for(int i=0; i<strAllList.size(); i++)
    {
        QVariantList qVarList;
        QStringList strOneList = strAllList.at(i);
        for(int j=0; j<qMin(strOneList.size(), m_strTitleList.size()); j++)
            qVarList.push_back(strOneList.at(j));

        pXlsxStruct->varWriteDataList.push_back(qVarList);
    }

    FunWriteXlsxEndCallBack lambdaFunction = [this](QString strXlsxName, QString strTableName)
    {
        Q_UNUSED(strTableName);

        qDebug()<<Q_FUNC_INFO<<QThread::currentThreadId();
        QString strDestPath = CPublicConfig::GetInstance()->GetUDiskExportDir();
        bool bCopy = CopyQFileDir(strXlsxName, QDir(strDestPath));
        this->m_pExportBar->close();
        System("sync");
        if(bCopy)
            ShowSuccess(this, m_strTipsText, tr("操作日志导出完成"));
        else
            ShowError(this, m_strTipsText, tr("操作日志导出失败，请检查U盘"));
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void COperationLogWidget::_ShowCurrentPageAllData()
{
    int iMidLen = m_iOnePageLines;
    if(m_iCurrentPage * m_iOnePageLines + m_iOnePageLines > m_strAllLogList.size())
        iMidLen = m_strAllLogList.size() - m_iCurrentPage * m_iOnePageLines;

    qDebug()<<Q_FUNC_INFO<<m_iCurrentPage<<m_iOnePageLines<<iMidLen<<m_strAllLogList.size();

    QList<QStringList> strReadList = m_strAllLogList.mid(m_iCurrentPage * m_iOnePageLines, iMidLen);
    _UpdateTableWidget(strReadList);
    _UpdateGroupBoxInfo();
}

void COperationLogWidget::_ShowCurrentPageQueryData()
{
    int iMidLen = m_iOnePageLines;
    if(m_iCurrentPage * m_iOnePageLines + m_iOnePageLines > m_strQueryLogList.size())
        iMidLen = m_strQueryLogList.size() - m_iCurrentPage * m_iOnePageLines;

    qDebug()<<Q_FUNC_INFO<<m_iCurrentPage<<m_iOnePageLines<<iMidLen<<m_strQueryLogList.size();

    QList<QStringList> strReadList = m_strQueryLogList.mid(m_iCurrentPage * m_iOnePageLines, iMidLen);
    _UpdateTableWidget(strReadList);
    _UpdateGroupBoxInfo();
}

void COperationLogWidget::_UpdateTableWidget(const QList<QStringList> &strList)
{
    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(strList.size());
    for(int iRow=0; iRow<strList.size(); iRow++)
    {
        QStringList oneRowList = strList.at(iRow);
        int iSize = qMin(oneRowList.size(), m_strTitleList.size());
        for(int iCol=0; iCol<iSize; iCol++)
        {
            QTableWidgetItem *pItem = new QTableWidgetItem;
            pItem->setText(oneRowList.at(iCol));
            pItem->setTextAlignment(Qt::AlignCenter);
            m_pTableWidget->setItem(iRow, iCol, pItem);
        }
    }
}

void COperationLogWidget::_UpdateGroupBoxInfo()
{
    if(m_bHasQuery)
        m_pLinesLabel->setText(tr("查询共%1条记录").arg(m_iTotalLines));
    else
        m_pLinesLabel->setText(tr("总共%1条记录").arg(m_iTotalLines));

    m_pPageLabel->setText(QString("%1/%2").arg(m_iCurrentPage + 1).arg(m_iTotalPages));

    if(m_iCurrentPage <= 0)
    {
        m_pPrePageBtn->setEnabled(false);
        m_pNextPageBtn->setEnabled(true);
    }
    else if(m_iCurrentPage >= m_iTotalPages - 1)
    {
        m_pPrePageBtn->setEnabled(true);
        m_pNextPageBtn->setEnabled(false);
    }
    else
    {
        m_pPrePageBtn->setEnabled(true);
        m_pNextPageBtn->setEnabled(true);
    }

    if(m_iTotalLines <= m_iOnePageLines)
    {
        m_pPageLabel->setText("1/1");
        m_pPrePageBtn->setEnabled(false);
        m_pNextPageBtn->setEnabled(false);
    }
}

void COperationLogWidget::_InitWidget()
{
    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setFixedSize(1636, 650);
    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setWordWrap(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setAlternatingRowColors(true);
    m_pTableWidget->setShowGrid(false);

    m_strTitleList << tr("序号") << tr("操作人") << tr("日期") << tr("内容");
    m_pTableWidget->setColumnCount(m_strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(m_strTitleList);
    m_pTableWidget->setRowCount(12);

    QHeaderView *pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);

    QHeaderView *pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 100);
    pHorizontalHeader->resizeSection(1, 220);
    pHorizontalHeader->resizeSection(2, 220);
    pHorizontalHeader->setStretchLastSection(true);
    pHorizontalHeader->setDisabled(true);

    m_pLinesLabel = new QLabel;
    m_pLinesLabel->setFixedHeight(40);
    m_pLinesLabel->setMinimumWidth(120);

    m_pGotoLabel1 = new QLabel(tr("跳转至"));
    m_pGotoLabel1->setObjectName("GotoLabel");

    m_pGotoLineEdit = new CLineEdit;
    m_pGotoLineEdit->setFixedSize(72, 40);
    m_pGotoLineEdit->setObjectName("GotoLineEdit");
    m_pGotoLineEdit->setAlignment(Qt::AlignCenter);

    m_pGotoLabel2 = new QLabel(tr("页"));
    m_pGotoLabel2->setObjectName("GotoLabel");

    m_pGotoBtn = new QPushButton(tr("跳转"));
    m_pGotoBtn->setFixedSize(72, 40);
    if(eLanguage_German == gk_iLanguage)
        m_pGotoBtn->setFixedSize(100, 40);
    m_pGotoBtn->setObjectName("GotoBtn");
    connect(m_pGotoBtn, &QPushButton::clicked, this, &COperationLogWidget::_SlotGotoPageBtn);

    m_pPrePageBtn = new QPushButton;
    m_pPrePageBtn->setFixedSize(32, 32);
    m_pPrePageBtn->setObjectName("PrePageBtn");
    connect(m_pPrePageBtn, &QPushButton::clicked, this, &COperationLogWidget::_SlotPrePageBtn);

    m_pPageLabel = new QLabel("0/0");
    m_pPageLabel->setObjectName("PageLabel");

    m_pNextPageBtn = new QPushButton;
    m_pNextPageBtn->setFixedSize(32, 32);
    m_pNextPageBtn->setObjectName("NextPageBtn");
    connect(m_pNextPageBtn, &QPushButton::clicked, this, &COperationLogWidget::_SlotNextPageBtn);

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &COperationLogWidget::SignalReturn);

    m_pQueryComboBox = new QComboBox;
    m_pQueryComboBox->setFixedSize(210, 56);
    m_pQueryComboBox->setView(new QListView);
    m_pQueryComboBox->addItems(m_strTitleList);
    m_pQueryComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pQueryComboBox->setMaxVisibleItems(10);
    m_pQueryComboBox->setStyle(QStyleFactory::create("Windows"));

    m_pQueryLineEdit = new CLineEdit;
    m_pQueryLineEdit->setFixedSize(240, 56);
    m_pQueryLineEdit->setPlaceholderText(tr("输入查询内容"));

    if(eLanguage_English == gk_iLanguage)
    {
        m_pQueryComboBox->setFixedSize(300, 56);
        m_pQueryLineEdit->setFixedSize(300, 56);
    }

    m_pQueryBtn = new QPushButton(tr("查询"));
    m_pQueryBtn->setFixedSize(150, 56);
    connect(m_pQueryBtn, &QPushButton::clicked, this, &COperationLogWidget::_SlotQueryBtn);

    m_pShowAllBtn = new QPushButton(tr("刷新"));
    m_pShowAllBtn->setFixedSize(150, 56);
    connect(m_pShowAllBtn, &QPushButton::clicked, this, &COperationLogWidget::_SlotShowAllBtn);

    m_pDetailBtn = new QPushButton(tr("详情"));
    m_pDetailBtn->setFixedSize(150, 56);
    connect(m_pDetailBtn, &QPushButton::clicked, this, &COperationLogWidget::_SlotDetailBtn);

    m_pExportBtn = new QPushButton(tr("导出"));
    m_pExportBtn->setFixedSize(150, 56);
    connect(m_pExportBtn, &QPushButton::clicked, this, &COperationLogWidget::_SlotExportBtn);
}

void COperationLogWidget::_InitLayout()
{
    QHBoxLayout *pPageLayout = new QHBoxLayout;
    pPageLayout->setMargin(0);
    pPageLayout->setSpacing(12);
    pPageLayout->addStretch(1);
    pPageLayout->addWidget(m_pLinesLabel);
    pPageLayout->addSpacing(30);
    pPageLayout->addWidget(m_pGotoLabel1);
    pPageLayout->addWidget(m_pGotoLineEdit);
    pPageLayout->addWidget(m_pGotoLabel2);
    pPageLayout->addWidget(m_pGotoBtn);
    pPageLayout->addSpacing(20);
    pPageLayout->addWidget(m_pPrePageBtn);
    pPageLayout->addSpacing(4);
    pPageLayout->addWidget(m_pPageLabel);
    pPageLayout->addSpacing(4);
    pPageLayout->addWidget(m_pNextPageBtn);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(10);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pReturnBtn);
    pBtnLayout->addSpacing(40);
    pBtnLayout->addWidget(m_pQueryComboBox);
    pBtnLayout->addWidget(m_pQueryLineEdit);
    pBtnLayout->addWidget(m_pQueryBtn);
    pBtnLayout->addSpacing(40);
    pBtnLayout->addWidget(m_pShowAllBtn);
    pBtnLayout->addSpacing(40);
    pBtnLayout->addWidget(m_pDetailBtn);
    pBtnLayout->addSpacing(40);
    pBtnLayout->addWidget(m_pExportBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTableWidget, 0, Qt::AlignHCenter);
    pLayout->addSpacing(15);
    pLayout->addLayout(pPageLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);
    this->setLayout(pLayout);
}
