#include "CPressureOneCurve.h"
#include <QBoxLayout>

#include "CRunTest.h"
#include "qcustomplot.h"
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicParams.h"
#include "PublicFunction.h"
#include "CSetChartXYRange.h"
#include "CReadWriteXlsxThread.h"
#include <QFont>

CPressureOneCurve::CPressureOneCurve(int iMachineID, QWidget *parent)
    : QWidget(parent)
    , m_bShow(false)
    , m_bReplot(false)
    , m_iMachineID(iMachineID)
{
    _InitWidget();

//    for(int i=0; i<100; i++)
//    {
//        m_dTimeVec << i;
//        m_dPreValueVec1 << i / 10.0;
//        m_dPreValueVec2 << i / 10.0 - 1;
//    }
//    m_pCustomPlot->graph(0)->setData(m_dTimeVec, m_dPreValueVec1);
//    m_pCustomPlot->graph(1)->setData(m_dTimeVec, m_dPreValueVec2);
//    m_pCustomPlot->replot();
}

void CPressureOneCurve::ClearData()
{
    //QStringList strRangeList = {"0", "1000", "-15", "15"};
    //m_pCSetChartXYRange->SetRange(strRangeList);

    m_dTimeVec.clear();
    m_dPreValueVec1.clear();
    m_dPreValueVec2.clear();
    m_beginDateTime = QDateTime::currentDateTime();

    QString strText = "pressure1:\npressure2:\np1-p2:";
    m_pCPItemText->setText(strText);

    QVector<double> x, y;
    m_pCustomPlot->graph(0)->setData(x, y);
    m_pCustomPlot->graph(1)->setData(x, y);
    m_pCustomPlot->replot();
}

void CPressureOneCurve::ExportData()
{
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    if(!UDiskExistAndCreateDir(strExportDir, this))
        return;

    QString strCurrentTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strXlsxName = QString("Pressure_%1#_%2.xlsx").arg(m_iMachineID + 1).arg(strCurrentTime);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "pressure";
    pXlsxStruct->strTitleList << "time" << "pressure1" << "pressure2";

    QList<QVariantList> &varDataList = pXlsxStruct->varWriteDataList;
    for(int i=0; i<m_dTimeVec.size(); i++)
    {
        QVariantList oneList;
        oneList << m_dTimeVec.at(i) << m_dPreValueVec1.at(i) << m_dPreValueVec2.at(i);
        varDataList<<oneList;
    }

    FunWriteXlsxEndCallBack lambdaFunction = [this](QString strXlsxName, QString strTableName)
    {
        Q_UNUSED(strTableName);
        qDebug()<<strXlsxName<<"文件导出完成";
        QString strDestPath = CPublicConfig::GetInstance()->GetUDiskExportDir();
        MoveQFile(strXlsxName, QDir(strDestPath));
        ExportEndUmountUSB();
        ShowInformation(this, tr("提示"), tr("文件导出完成"));
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CPressureOneCurve::TestEnd()
{
    //自检不生成xlsx
    QString strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(m_iMachineID);
    if(strXlsxName.isEmpty())
        return;

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "pressure";
    pXlsxStruct->bDrawChart = true;
    pXlsxStruct->strTitleList << "time" << "pressure1" << "pressure2";

    int size = m_dTimeVec.size();
    for(int i=0; i<size; i++)
    {
        QVariantList qRowList = {m_dTimeVec.at(i), m_dPreValueVec1.at(i), m_dPreValueVec2.at(i)};
        pXlsxStruct->varWriteDataList << qRowList;
    }

    ChartNoteStruct chart;
    chart.iRow = 4;
    chart.iColumn = 5;
    chart.strChartTitle = "pressure data";
    chart.strXTitle = "time (s)";
    chart.strYTitle = "pressure (kpa)";
    chart.strSerialNameList << "kpa";
    chart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(size + 1);
    chart.strNumDataRange = QString("B2:C%1").arg(size + 1);
    chart.bMajorGridlines = false;
    chart.strMarkSymbolList << "none" << "none";

    pXlsxStruct->chartNoteList << chart;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    qDebug()<<QString("%1# pressure data write to result xlsx end").arg(m_iMachineID + 1);
}

void CPressureOneCurve::ReceiveData(const QVariant &qVarData)
{
    //除以10000
    QVariantList qVarList = qVarData.toList();
    if(qVarList.isEmpty())
        return;
    double dPreValue1 = qVarList.at(0).toDouble() / 10000.0;
    dPreValue1 *= 6.89;
    m_dPreValueVec1.push_back(dPreValue1);

    double dPreValue2 = 0;
    if(qVarList.size() >= 2)
    {
        dPreValue2 = qVarList.at(1).toDouble() / 10000.0;
        dPreValue2 *= 6.89;        
    }
    m_dPreValueVec2.push_back(dPreValue2);

    QString strText = QString("pressure1:%1\npressure2:%2\np1-p2:%3")
            .arg(dPreValue1).arg(dPreValue2).arg(dPreValue1-dPreValue2);
    m_pCPItemText->setText(strText);

    //秒
    double dTime = 0;
    if(m_dTimeVec.isEmpty())
        m_beginDateTime = QDateTime::currentDateTime();
    else
        dTime = m_beginDateTime.msecsTo(QDateTime::currentDateTime()) / 1000.0;
    m_dTimeVec.push_back(dTime);

    m_bReplot = true;
    if(m_bShow)
    {        
        m_bReplot = false;
        m_pCustomPlot->graph(0)->setData(m_dTimeVec, m_dPreValueVec1);
        m_pCustomPlot->graph(1)->setData(m_dTimeVec, m_dPreValueVec2);
        m_pCustomPlot->replot();
    }
}

void CPressureOneCurve::GetData(QVector<double> &dTimeVec, QVector<double> &dPreValueVec1, QVector<double> &dPreValueVec2)
{
    dTimeVec = m_dTimeVec;
    dPreValueVec1 = m_dPreValueVec1;
    dPreValueVec2 = m_dPreValueVec2;
}

void CPressureOneCurve::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    if(m_bReplot)
    {
        m_bReplot = false;
        m_pCustomPlot->graph(0)->setData(m_dTimeVec, m_dPreValueVec1);
        m_pCustomPlot->graph(1)->setData(m_dTimeVec, m_dPreValueVec2);
        m_pCustomPlot->replot();
    }
    QWidget::showEvent(pEvent);
}

void CPressureOneCurve::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CPressureOneCurve::_SlotSetXYRange(const QStringList &strList)
{
    if(4 != strList.size())
        return;

    m_pCustomPlot->xAxis->setRange(strList.at(0).toDouble(), strList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strList.at(2).toDouble(), strList.at(3).toDouble());
    m_pCustomPlot->replot();
}

void CPressureOneCurve::_SlotPlotClicked(QCPAbstractPlottable *pPlottable, int iDataIndex, QMouseEvent *pEvent)
{
    Q_UNUSED(pEvent);
    QString strName = pPlottable->name();
    double dx=0, dy1=0, dy2=0, dDiff=0;

    if("pressure1" == strName)
    {
        const QCPGraphData *pGraph = m_pCustomPlot->graph(0)->data()->at(iDataIndex);
        dx = pGraph->key;
        dy1 = pGraph->value;
        if(dx < m_dPreValueVec2.size())
            dy2 = m_dPreValueVec2.at(dx);
        dDiff = dy1 - dy2;
    }
    else
    {
        const QCPGraphData *pGraph = m_pCustomPlot->graph(1)->data()->at(iDataIndex);
        dx = pGraph->key;
        dy2 = pGraph->value;
        if(dx < m_dPreValueVec1.size())
            dy1 = m_dPreValueVec1.at(dx);
        dDiff = dy1 - dy2;
    }

    QString strText = QString("pressure1:%1\npressure2:%2\np1-p2:%3").arg(dy1).arg(dy2).arg(dDiff);
    m_pCPItemText->setText(strText);
    qDebug()<<Q_FUNC_INFO<<strName<<dx<<dy1<<dy2<<dDiff<<strText;
    m_pCustomPlot->replot();
}

void CPressureOneCurve::_InitWidget()
{
    _InitCustomPlot();

    QStringList strRangeList = {"0", "80", "0", "80"};
    m_pCSetChartXYRange = new CSetChartXYRange(strRangeList);
    m_pCSetChartXYRange->SetLineEditTextAlignment();
    connect(m_pCSetChartXYRange, &CSetChartXYRange::SignalSetRange, this, &CPressureOneCurve::_SlotSetXYRange);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pCustomPlot);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCSetChartXYRange, 0, Qt::AlignLeft);
    this->setLayout(pLayout);
}

void CPressureOneCurve::_InitCustomPlot()
{
    m_pCustomPlot = new QCustomPlot;

    QFont font;
    font.setPointSize(10);
    m_pCustomPlot->legend->setFont(font);
    m_pCustomPlot->legend->setSelectedFont(font);
    m_pCustomPlot->legend->setVisible(true);
    m_pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pCustomPlot->legend->setWrap(1);
    m_pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    _AddGraph(m_pCustomPlot, Qt::blue, Qt::blue, 0, "pressure1");
    _AddGraph(m_pCustomPlot, Qt::green, Qt::green, 1, "pressure2");

    m_pCustomPlot->xAxis->setRange(0, 80);
    m_pCustomPlot->yAxis->setRange(0, 80);
    m_pCustomPlot->xAxis->setLabel("time/s");
    m_pCustomPlot->yAxis->setLabel("Kpa");

    m_pCustomPlot->yAxis->setSubTicks(false);
    m_pCustomPlot->yAxis->ticker()->setTickCount(30);

    connect(m_pCustomPlot, &QCustomPlot::plottableClick, this, &CPressureOneCurve::_SlotPlotClicked);

    QCPItemText *pCPItemText = new QCPItemText(m_pCustomPlot);
    pCPItemText->setTextAlignment(Qt::AlignCenter);

    pCPItemText->setFont(QFont(this->font().family(), 12));
    pCPItemText->setPen(QPen(Qt::black));
    pCPItemText->setBrush(QBrush(QColor("#a1ffa1")));
    pCPItemText->setPadding(QMargins(5, 5, 5, 5));

    QString strText = "pressure1:\npressure2:\np1-p2:";
    pCPItemText->setText(strText);
    pCPItemText->setVisible(true);
    pCPItemText->position->setType(QCPItemPosition::ptAxisRectRatio);
    pCPItemText->position->setCoords(0.8, 0.05);
    m_pCPItemText = pCPItemText;
}

void CPressureOneCurve::_AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName)
{
    QPen pen;
    pen.setWidth(2);
    pen.setColor(penColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    pCustomPlot->graph(iChart)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssNone,
                                                                QPen(pointColor, 2),
                                                                QBrush(pointColor), 2));
}
