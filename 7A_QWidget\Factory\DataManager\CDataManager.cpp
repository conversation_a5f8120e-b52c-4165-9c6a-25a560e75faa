#include "CDataManager.h"
#include <QBoxLayout>

#include "CPressureInfo.h"
#include "CFaultCodeManager.h"
#include "CFaultLog.h"
#include "CRunLog.h"
#include "CProjectManager.h"
#include "CDataExport.h"
#include "CSelfTestParams.h"

CDataManager::CDataManager(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
}

CDataManager::~CDataManager()
{

}

void CDataManager::GotoRunLog()
{
    m_pCHBtnTitle->SetTitleIndex(3);
}

void CDataManager::_SlotTitleChanged(int index)
{
    m_pStackedWidget->setCurrentIndex(index);
}

void CDataManager::_InitWidget()
{
    QStringList strList = {tr("压力数据"), tr("故障码管理"), tr("故障日志"), tr("运行日志"), tr("项目管理"), tr("数据导出"), tr("自检参数")};
    m_pCHBtnTitle = new CHBtnTitleWidget(strList);
    m_pCHBtnTitle->SetTitleIndex(0);
    connect(m_pCHBtnTitle, &CHBtnTitleWidget::SignalTitleChanged, this, &CDataManager::_SlotTitleChanged);

    m_pCPressureInfo = new CPressureInfo;
    m_pCFaultCodeManager = new CFaultCodeManager;
    m_pCFaultLog = new CFaultLog;
    connect(m_pCFaultCodeManager, &CFaultCodeManager::SignalUpdateCodeMap, m_pCFaultLog, &CFaultLog::SlotUpdateCodeInfoMap);
    m_pCRunLog = new CRunLog;
    m_pCProjectManager = new CProjectManager;
    m_pCDataExport = new CDataExport;
    m_pCSelfTestParams = new CSelfTestParams;

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->addWidget(m_pCPressureInfo);
    m_pStackedWidget->addWidget(m_pCFaultCodeManager);
    m_pStackedWidget->addWidget(m_pCFaultLog);
    m_pStackedWidget->addWidget(m_pCRunLog);
    m_pStackedWidget->addWidget(m_pCProjectManager);
    m_pStackedWidget->addWidget(m_pCDataExport);
    m_pStackedWidget->addWidget(m_pCSelfTestParams);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCHBtnTitle);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pStackedWidget);
    this->setLayout(pLayout);
}
