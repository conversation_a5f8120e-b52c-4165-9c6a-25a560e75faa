#include "CHomeDevItemWidget.h"
#include <QDebug>
#include <QBoxLayout>

#include "CRunTest.h"
#include "CHeartBeat.h"
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "COperationUnit.h"
#include "CCheckUserWidget.h"
#include "CLotInfoDB.h"

CHomeDevItemWidget::CHomeDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent)
    : QWidget(parent)
    , m_iMachineID(iMachineID)
    , m_sDevParams(sDevParams)
    , m_eLastStatus(eDeviceDisconnect)
    , m_eCurrentStatus(eDeviceDisconnect)
    , m_eLastHeartbeatStatus(eDeviceDisconnect)
{
    m_bHasCardbox = false;
    m_bHrmTest = false;
    m_iRunSecond = 1020;
    //m_iRunSecond = CPublicConfig::GetInstance()->GetRunTimeMinute() * 60;

    this->setFixedSize(m_sDevParams.iItemWidth, m_sDevParams.iItemHeight);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    //m_pDetailWidget = new CHistoryDetailWidget(tr("测试详情"), false, iMachineID);

    m_pRunTimer = new QTimer(this);
    connect(m_pRunTimer, &QTimer::timeout, this, &CHomeDevItemWidget::_SlotRunTimer);

    m_pProcessingTimer = new QTimer(this);
    connect(m_pProcessingTimer, &QTimer::timeout, this, &CHomeDevItemWidget::_SlotProcessingTimer);

    connect(CRunTest::GetInstance(), &CRunTest::SignalUpdateItemStatus,
            this, &CHomeDevItemWidget::SlotUpdateItemStatus);
    connect(CHeartBeat::GetInstance(), &CHeartBeat::SignalUpdateItemStatus,
            this, &CHomeDevItemWidget::SlotUpdateHeartStatus);
    connect(CHeartBeat::GetInstance(), &CHeartBeat::SignalResetItemStatus,
            this, &CHomeDevItemWidget::SlotResetStatus);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSelfTestEnd,
            this, &CHomeDevItemWidget::SlotSelfTestEnd);

    SetDeviceStatus(m_eCurrentStatus);
}

void CHomeDevItemWidget::SetDeviceStatus(DeviceStatus eStatus)
{
    qDebug()<<Q_FUNC_INFO<<m_iMachineID<<eStatus;

    //更新工厂模式里的仪器信息和校准的状态
    emit CPublicConfig::GetInstance()->SignalSetDevStatus(m_iMachineID, eStatus);

    m_eLastStatus = m_eCurrentStatus;
    m_eCurrentStatus = eStatus;

    bool bActBtnEnable = true;
    QString strPropertyText, strTipsText, strActText;
    switch (eStatus)
    {
    case eDeviceDisconnect:
        strPropertyText = "disconnect";
        strTipsText = tr("离线中");
        strActText = tr("仪器不可用");
        bActBtnEnable = false;
        break;
    case eDeviceSelfTest:
        strPropertyText = "self_test";
        strTipsText = tr("自检中");
        strActText = tr("仪器不可用");
        bActBtnEnable = false;
        m_pRunTimer->stop();
        m_pProcessingTimer->stop();
        m_pTimeLabel->setText("00:00");
        break;
    case eDeviceIdle:
        strPropertyText = "idle";
        strTipsText = tr("空闲中");
        if(m_bHasCardbox)
            strActText = tr("取出试剂卡");
        else
            strActText = tr("创建测试");
        _ClearData();
        break;
    case eDeviceTesting:
        strPropertyText = "testing";
        strTipsText = tr("测试中");
        strActText = tr("停止测试");
        if(!m_pRunTimer->isActive())
        {
            m_qRemainTime = QTime(0, 0);
            m_qRemainTime = m_qRemainTime.addSecs(m_iRunSecond);
            m_pRunTimer->start(1000);
        }
        break;
    case eDeviceTestDone:
        strPropertyText = "test_done";
        strTipsText = tr("测试完成");
        strActText = tr("查看结果");
        m_pRunTimer->stop();
        m_pProcessingTimer->stop();
        m_pTimeLabel->setText("00:00");
        break;
    case eDeviceTestStopped:
        strPropertyText = "test_stopped";
        strTipsText = tr("测试已停止");
        strActText = tr("查看结果");
        m_pRunTimer->stop();
        m_pProcessingTimer->stop();
        m_pTimeLabel->setText("00:00");
        break;
    case eDeviceTestStopping:
    {
        strPropertyText = "test_stopping";
        strTipsText = tr("停止中");
        strActText = tr("正在停止");
        m_pRunTimer->stop();
        m_pProcessingTimer->stop();
        SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID);
        sRunInfo.bRunning = false;
        break;
    }
    case eDeviceTestFail:
        strPropertyText = "test_fail";
        strTipsText = tr("测试失败");
        strActText = tr("查看结果");
        m_pRunTimer->stop();
        m_pProcessingTimer->stop();
        break;
    case eDeviceFault:
        strPropertyText = "fault";
        strTipsText = tr("故障中");
        strActText = tr("仪器不可用");
        bActBtnEnable = false;
        m_pRunTimer->stop();
        m_pProcessingTimer->stop();
        m_pTimeLabel->setText("00:00");
        break;
    case eDeviceProcessing:
        strPropertyText = "processing";
        strTipsText = tr("处理中");
        strActText = tr("停止测试");
        bActBtnEnable = true;
        break;
    case eDeviceReset:
        strPropertyText = "reset";
        strTipsText = tr("复位中");
        strActText = tr("仪器不可用");
        bActBtnEnable = false;
        break;
    default:
        break;
    }

    m_pTitleLabel->setProperty("status", strPropertyText);
    m_pTitleLabel->style()->polish(m_pTitleLabel);

    m_pIndexLabel->setProperty("status", strPropertyText);
    m_pIndexLabel->style()->polish(m_pIndexLabel);

    m_pTipsLabel->setProperty("status", strPropertyText);
    m_pTipsLabel->style()->polish(m_pTipsLabel);
    m_pTipsLabel->setText(strTipsText);

    m_pTimeLabel->setProperty("status", strPropertyText);
    m_pTimeLabel->style()->polish(m_pTimeLabel);

    m_pActIconLabel->setProperty("status", strPropertyText);
    m_pActIconLabel->style()->polish(m_pActIconLabel);

    m_pActionBtn->setProperty("status", strPropertyText);
    m_pActionBtn->style()->polish(m_pActionBtn);
    m_pActionBtn->setEnabled(bActBtnEnable);

    m_pActTextLabel->setText(strActText);
}

void CHomeDevItemWidget::SetCardSampleInfo(const SCardInfoStruct &sCardInfo, const SSampleInfoStruct &sSampleInfo)
{    
    m_sCardInfo = sCardInfo;
    m_sSampleInfo = sSampleInfo;

    m_pSampleIDLabel->SetValueLabelText(sSampleInfo.strSampleID);
    m_pCardIDLabel->SetValueLabelText(sCardInfo.strCardID);
    m_pProjectLabel->SetValueLabelText(sCardInfo.strProject);
    m_pNameLabel->SetValueLabelText(sSampleInfo.strName);
    m_pGenderLabel->SetValueLabelText(sSampleInfo.strGender);

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID);
    m_bHrmTest = bHrmTecType(sRunInfo.iTecIndex);

    // 这里去找数据库内的数据最好
    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sCardInfo.strProject,sLotInfo);
    if(m_bHrmTest)
    {
        m_iRunSecond = sLotInfo.iHrmTime;
        if(m_iRunSecond <= 60)
        {
            m_iRunSecond = 7200;
        }
    }
    else
    {
        m_iRunSecond = sLotInfo.iTestTime;
        if(m_iRunSecond <= 60)
        {
            m_iRunSecond = 1080;
        }
    }

#if 0
    if(m_bHrmTest)
        m_iRunSecond = 7200;
    else
        m_iRunSecond = CPublicConfig::GetInstance()->GetRunTimeMinute() * 60;
#endif
    qDebug()<<Q_FUNC_INFO<<"测试倒计时:"<<m_iMachineID<<m_bHrmTest<<m_iRunSecond;
    SetDeviceStatus(eDeviceTesting);
}

void CHomeDevItemWidget::SetFLDataMap(int iMachineID, const QList<QMap<double, double> > &dFLMap)
{
    //m_pDetailWidget->SetFLDataMap(iMachineID,dFLMap);
}

void CHomeDevItemWidget::SetMeltingFLDataMap(int iMachineID, const QList<double> &dTempList, const QList<QMap<double, double> > &dFLMap)
{
    //m_pDetailWidget->SetMeltingFLDataMap(iMachineID,dTempList,dFLMap);
}

void CHomeDevItemWidget::ClearData()
{
    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID);
    m_bHrmTest =  bHrmTecType(sRunInfo.iTecIndex);

    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sRunInfo.sCardInfo.strProject,sLotInfo);
    if(m_bHrmTest)
    {
        m_iRunSecond = sLotInfo.iHrmTime;
        if(m_iRunSecond <= 60)
        {
            m_iRunSecond = 7200;
        }
    }
    else
    {
        m_iRunSecond = sLotInfo.iTestTime;
        if(m_iRunSecond <= 60)
        {
            m_iRunSecond = 1080;
        }
    }

#if 0
    if(m_bHrmTest)
        m_iRunSecond = 7200;
    else
        m_iRunSecond = CPublicConfig::GetInstance()->GetRunTimeMinute() * 60;
#endif

    m_qRemainTime = QTime(0, 0);
    m_qRemainTime = m_qRemainTime.addSecs(m_iRunSecond);
    qDebug()<<Q_FUNC_INFO<<"测试倒计时:"<<m_iMachineID<<m_bHrmTest<<m_iRunSecond;
    emit SingalClearFlData(m_iMachineID);
    //m_pDetailWidget->ClearData();
    //m_pDetailWidget->ClearFL();
}

//从创建测试来的状态
void CHomeDevItemWidget::SlotUpdateItemStatus(int iMachineID, DeviceStatus eStatus)
{
    if(m_iMachineID != iMachineID)
        return;

    bool bSelfTest = CPublicConfig::GetInstance()->GetSelfTestRunning(iMachineID);
    qDebug()<<Q_FUNC_INFO<<iMachineID<<eStatus<<bSelfTest;

    //上位机自检中,不能更新状态
    if(bSelfTest)
        return;

    SetDeviceStatus(eStatus);

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID);

    //if(m_pDetailWidget->isVisible())
    //{
    //    m_pDetailWidget->UpdateInfo(sRunInfo.sResultInfo.iHistoryID);
    //}

    // 工厂模式创建测试 重新获取样本卡盒信息
    if(eDeviceTesting == eStatus)
    {
        if(sRunInfo.bFactroyTest)
        {
            m_sCardInfo = sRunInfo.sCardInfo;
            m_sSampleInfo = sRunInfo.sSampleInfo;

            m_pSampleIDLabel->SetValueLabelText(m_sSampleInfo.strSampleID);
            m_pCardIDLabel->SetValueLabelText(m_sCardInfo.strCardID);
            m_pProjectLabel->SetValueLabelText(m_sCardInfo.strProject);
            m_pNameLabel->SetValueLabelText(m_sSampleInfo.strName);
            m_pGenderLabel->SetValueLabelText(m_sSampleInfo.strGender);
        }
    }
}

//从心跳来的状态
void CHomeDevItemWidget::SlotUpdateHeartStatus(int iMachineID, DeviceStatus eStatus, bool bCardExist)
{
    if(m_iMachineID != iMachineID)
        return;

    bool bSelfTest = CPublicConfig::GetInstance()->GetSelfTestRunning(iMachineID);
    qDebug()<<Q_FUNC_INFO<<iMachineID<<eStatus<<bCardExist<<m_eCurrentStatus<<m_eLastStatus<<bSelfTest;

    m_eLastHeartbeatStatus = eStatus;
    m_bHasCardbox = bCardExist;

    //上位机自检中,不采用下位机上传的状态
    if(bSelfTest)
        return;

    if(eDeviceDisconnect == m_eCurrentStatus)
    {
        if(eDeviceTesting != m_eLastStatus)
        {
            SetDeviceStatus(eStatus);
            return;
        }

        //上一次状态是测试中
        //自检
        if(eDeviceSelfTest == eStatus)
        {
            SetDeviceStatus(eDeviceTesting);
            return;
        }
        //空闲或测试完成
        if(eDeviceIdle == eStatus || eDeviceTestDone == eStatus)
        {
            SetDeviceStatus(eDeviceTesting);
            // todo 检索FL,FL不够就失败，够就计算CT
            return;
        }
        //测试中或测试失败
        if(eDeviceTesting == eStatus || eDeviceTestFail == eStatus)
        {
            SetDeviceStatus(eStatus);
            return;
        }
        //故障中
        if(eDeviceFault == eStatus)
        {
            SetDeviceStatus(eStatus);
            // TODO 历史更新为测试失败
            return;
        }
        return;
    }

    if(eDeviceTesting == m_eCurrentStatus)
    {
        if(eDeviceSelfTest == eStatus)
        {
            SetDeviceStatus(eDeviceTesting);
            return;
        }
        if(eDeviceIdle == eStatus || eDeviceTestDone == eStatus)
        {
            SetDeviceStatus(eDeviceTesting);
            // todo 检索FL,FL不够就失败，够就计算CT
            return;
        }
        if(eDeviceFault == eStatus)
        {
            SetDeviceStatus(eStatus);
            // TODO 历史更新为测试失败
            return;
        }
        if(eDeviceTesting == eStatus)
        {
            SetDeviceStatus(eStatus);
            return;
        }
        return;
    }

    if(eDeviceTestDone == m_eCurrentStatus ||
            eDeviceTestFail == m_eCurrentStatus ||
            eDeviceTestStopped == m_eCurrentStatus ||
            eDeviceProcessing == m_eCurrentStatus)
    {
        return;
    }

    SetDeviceStatus(eStatus);
}

void CHomeDevItemWidget::SlotResetStatus(int iMachineID)
{
    return;

    if(iMachineID != m_iMachineID)
        return;

    qDebug()<<QString("%1#断联后重置状态:%2 %3").arg(iMachineID + 1).arg(m_eLastStatus).arg(m_eCurrentStatus);
    if(eDeviceDisconnect == m_eCurrentStatus)
        SetDeviceStatus(m_eLastStatus);
}

void CHomeDevItemWidget::SlotSelfTestEnd(int iMachineID, int iResult)
{
    if(iMachineID != m_iMachineID)
        return;

    //根据自检结果来设置仪器状态
    qDebug()<<Q_FUNC_INFO<<QString("%1#自检结束,上次状态:%2").arg(iMachineID + 1).arg(m_eLastHeartbeatStatus);
    if(0 == iResult)
        SetDeviceStatus(m_eLastHeartbeatStatus);
    else
        SetDeviceStatus(eDeviceFault);
}

void CHomeDevItemWidget::mousePressEvent(QMouseEvent *pEvent)
{
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    if(eDeviceTesting == m_eCurrentStatus ||
            eDeviceTestStopped == m_eCurrentStatus ||
            eDeviceTestFail == m_eCurrentStatus ||
            eDeviceTestStopping == m_eCurrentStatus ||
            eDeviceTestDone == m_eCurrentStatus)
    {
        _DetailWidgetShow();

        if(eDeviceTestStopped == m_eCurrentStatus || eDeviceTestFail == m_eCurrentStatus)
        {
            SetDeviceStatus(eDeviceIdle);
        }

        if(eDeviceTestDone == m_eCurrentStatus)
        {
            qDebug()<<"测试完成点击后设置上一次心跳状态:"<<m_eLastHeartbeatStatus<<m_iMachineID;
            SetDeviceStatus(m_eLastHeartbeatStatus);
        }
    }

    QWidget::mousePressEvent(pEvent);
}

void CHomeDevItemWidget::_SlotActionBtn()
{
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    qDebug()<<Q_FUNC_INFO<<"机器ID:"<<m_iMachineID<<"机器状态:"<<m_eCurrentStatus
           <<"标题文字:"<<m_pTipsLabel->text()<<"按钮文字:"<<m_pActTextLabel->text();
    if(eDeviceIdle == m_eCurrentStatus)
    {
        if(!m_bHasCardbox)
        {
            ClearData();
            emit SignalCreateTest(m_iMachineID);
        }
    }
    else if(eDeviceTesting == m_eCurrentStatus || eDeviceProcessing == m_eCurrentStatus)
    {
        bool bCheckUser = ShowCheckUser(tr("%1#停止测试").arg(m_iMachineID + 1), (QWidget*)gk_pMainWindow);
        if(false == bCheckUser)
            return;

        qDebug()<<Q_FUNC_INFO<<QString("%1#手动停止测试").arg(m_iMachineID + 1);
        m_pRunTimer->stop();
        m_pProcessingTimer->stop();
        QString strStopCmd = CCmdBase::GetJsonCmdString(Method_stop);
        COperationUnit::GetInstance()->SendJsonText(m_iMachineID, Method_stop, strStopCmd);
        SetDeviceStatus(eDeviceTestStopping);
    }
    else if(eDeviceTestStopped == m_eCurrentStatus)
    {
        _DetailWidgetShow();
        SetDeviceStatus(eDeviceIdle);
    }
    else if(eDeviceTestDone == m_eCurrentStatus)
    {
        _DetailWidgetShow();
        SetDeviceStatus(eDeviceIdle);
    }
    else if(eDeviceTestFail == m_eCurrentStatus)
    {
        _DetailWidgetShow();
        SetDeviceStatus(eDeviceIdle);
    }
}

void CHomeDevItemWidget::_SlotRunTimer()
{
    m_iRunSecond--;
    if(m_iRunSecond <= 0)
    {
        RUN_LOG(QString("%1#倒计时结束,进入处理中状态90秒").arg(m_iMachineID + 1));
        m_pRunTimer->stop();
        m_pProcessingTimer->start(90000);
        SetDeviceStatus(eDeviceProcessing);
        //QString strStopCmd = CCmdBase::GetJsonCmdString(Method_stop);
        //COperationUnit::GetInstance()->SendJsonText(m_iMachineID, Method_stop, strStopCmd);
        //SetDeviceStatus(eDeviceTestStopping);
        return;
    }

    m_qRemainTime = m_qRemainTime.addSecs(-1);
    if(m_bHrmTest)
    {
        m_pTimeLabel->setText(m_qRemainTime.toString("hh:mm:ss"));
        qDebug()<<Q_FUNC_INFO<<"测试倒计时:"<<m_iMachineID<<m_bHrmTest<<m_iRunSecond<<m_pTimeLabel->text();
    }
    else
    {
        m_pTimeLabel->setText(m_qRemainTime.toString("mm:ss"));
        qDebug()<<Q_FUNC_INFO<<"测试倒计时:"<<m_iMachineID<<m_bHrmTest<<m_iRunSecond<<m_pTimeLabel->text();
    }
}

void CHomeDevItemWidget::_SlotProcessingTimer()
{
    RUN_LOG(QString("%1#处理中状态超时,正在停止测试").arg(m_iMachineID + 1));
    m_pProcessingTimer->stop();

    QString strStopCmd = CCmdBase::GetJsonCmdString(Method_stop);
    COperationUnit::GetInstance()->SendJsonText(m_iMachineID, Method_stop, strStopCmd);
    SetDeviceStatus(eDeviceTestStopping);
}

void CHomeDevItemWidget::_ClearData()
{
    m_pTimeLabel->setText("00:00");
    m_pRunTimer->stop();
    m_pProcessingTimer->stop();

    m_pSampleIDLabel->SetValueLabelText("");
    m_pCardIDLabel->SetValueLabelText("");
    m_pProjectLabel->SetValueLabelText("");
    m_pNameLabel->SetValueLabelText("");
    m_pGenderLabel->SetValueLabelText("");

    m_sCardInfo.Clear();
    m_sSampleInfo.Clear();
}

void CHomeDevItemWidget::_DetailWidgetShow()
{
    emit SingalDetailWidgetShow(m_iMachineID);
    /*
    int iHistoryID = CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID).sResultInfo.iHistoryID;
    m_pDetailWidget->SetHistoryID(iHistoryID);
    if(m_pDetailWidget->isVisible())
        m_pDetailWidget->close();
    m_pDetailWidget->show();
    */
}

void CHomeDevItemWidget::_InitTitleLabel()
{
    int iHeight = m_sDevParams.iTitleHeight;

    m_pIndexLabel = new QLabel(QString("%1").arg(m_iMachineID + 1));
    m_pIndexLabel->setFixedSize(m_sDevParams.iIndexWidth, iHeight);
    m_pIndexLabel->setObjectName("IndexLabel");
    m_pIndexLabel->setAlignment(Qt::AlignCenter);
    m_pIndexLabel->setProperty("status", "idle");

    m_pTipsLabel = new QLabel(tr("空闲中"));
    m_pTipsLabel->setFixedHeight(iHeight);
    m_pTipsLabel->setObjectName("StatusLabel");
    m_pTipsLabel->setProperty("status", "idle");

    m_pTimeLabel = new QLabel("00:00");
    m_pTimeLabel->setFixedHeight(iHeight);
    m_pTimeLabel->setObjectName("TimeLabel");
    m_pTimeLabel->setProperty("status", "idle");

    m_pTitleLabel = new QLabel;
    m_pTitleLabel->setFixedSize(m_sDevParams.iItemWidth, iHeight);
    m_pTitleLabel->setObjectName("TitleLabel");
    m_pTitleLabel->setWindowOpacity(0.14);
    m_pTitleLabel->setProperty("status", "idle");

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addWidget(m_pIndexLabel);
    pTitleLayout->addSpacing(15);
    pTitleLayout->addWidget(m_pTipsLabel);
    pTitleLayout->addStretch(1);
    pTitleLayout->addWidget(m_pTimeLabel);
    pTitleLayout->addSpacing(20);

    m_pTitleLabel->setLayout(pTitleLayout);
}

void CHomeDevItemWidget::_InitActButton()
{
    m_pActIconLabel = new QLabel;
    m_pActIconLabel->setFixedSize(m_sDevParams.iActIconSize, m_sDevParams.iActIconSize);
    m_pActIconLabel->setObjectName("ActIconLabel");
    m_pActIconLabel->setProperty("status", "idle");

    m_pActTextLabel = new QLabel(tr("创建测试"));
    //m_pActTextLabel->setFixedHeight(m_sDevParams.iActIconSize);
    m_pActTextLabel->setObjectName("ActTextLabel");

    m_pActionBtn = new QPushButton;
    m_pActionBtn->setObjectName("ActBtn");
    m_pActionBtn->setProperty("status", "idle");
    m_pActionBtn->setFixedSize(m_sDevParams.iItemWidth, m_sDevParams.iTitleHeight);

    connect(m_pActionBtn, &QPushButton::clicked, this, &CHomeDevItemWidget::_SlotActionBtn);

    QHBoxLayout *pActLayout = new QHBoxLayout;
    pActLayout->setMargin(0);
    pActLayout->setSpacing(0);
    pActLayout->addStretch(1);
    pActLayout->addWidget(m_pActIconLabel);
    pActLayout->addSpacing(m_sDevParams.iActSpacing);
    pActLayout->addWidget(m_pActTextLabel);
    pActLayout->addStretch(1);

    m_pActionBtn->setLayout(pActLayout);
}

QGroupBox *CHomeDevItemWidget::_CreateGroupBox()
{
    _InitTitleLabel();

    int iNameWidth = m_sDevParams.iNameWidth;
    int iValueWidth = m_sDevParams.iValueWidth;
    int iLabelHeight = m_sDevParams.iLabelHeight;

    m_pSampleIDLabel = new CLabelLabel(tr("样本编号："));
    m_pSampleIDLabel->setFixedSize(iNameWidth + iValueWidth + 5, iLabelHeight);

    m_pCardIDLabel = new CLabelLabel(tr("试剂卡编号："));
    m_pCardIDLabel->setFixedSize(iNameWidth + iValueWidth + 5, iLabelHeight);

    m_pProjectLabel = new CLabelLabel(tr("项目："));
    m_pProjectLabel->setFixedSize(iNameWidth + iValueWidth + 5, iLabelHeight);

    m_pNameLabel = new CLabelLabel(tr("姓名："));
    m_pNameLabel->setFixedSize(iNameWidth + iValueWidth + 5, iLabelHeight);

    m_pGenderLabel = new CLabelLabel(tr("性别："));
    m_pGenderLabel->setFixedSize(iNameWidth + iValueWidth + 5, iLabelHeight);

    _InitActButton();

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTitleLabel);
    pLayout->addSpacing(m_sDevParams.iAddSpacing);
    pLayout->addWidget(m_pSampleIDLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(m_sDevParams.iLabelSpacing);
    pLayout->addWidget(m_pCardIDLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(m_sDevParams.iLabelSpacing);
    pLayout->addWidget(m_pProjectLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(m_sDevParams.iLabelSpacing);
    pLayout->addWidget(m_pNameLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(m_sDevParams.iLabelSpacing);
    pLayout->addWidget(m_pGenderLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pActionBtn);

    if(m_sDevParams.iItemHeight < 230)
    {
        m_pNameLabel->setVisible(false);
        m_pGenderLabel->setVisible(false);
    }

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(m_sDevParams.iItemWidth, m_sDevParams.iItemHeight);
    pGroupBox->setObjectName("ItemGroupBox");
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
