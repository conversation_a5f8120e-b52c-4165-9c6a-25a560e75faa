#ifndef GLOBALPARAMS_H
#define GLOBALPARAMS_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-27
  * Description: 全局变量,枚举,结构体
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMap>
#include <QObject>
#include <QColor>
#include <QPixmap>
#include <QDateTime>

extern QSize G_QRootSize;   //主窗口大小
extern QPoint G_QRootPoint; //主窗口坐标
extern int gk_iLanguage; //当前语言类型
extern const QWidget* gk_pMainWindow; //主窗口指针

static const int gk_iBackTransparency = 20; //弹窗背景透明度
static const int gk_iMachineCount = 8; //一拖8
static const int gk_iHoleCount = 2;    //每台机器2个孔
static const int gk_iBGYRCount = 4;    //每个孔4个颜色, B G Y R
static const QStringList gk_strMachineNameList = {"1#", "2#", "3#", "4#",
                                                  "5#", "6#", "7#", "8#"};
static const QStringList gk_strColorNameList = {"B", "G", "Y", "R"};

static const QStringList gk_strErrTextList = {"","假阳", "假阴","Ct偏小","Ct偏大","测试状态异常，可忽略","其他"};
static const QString gk_strTipsText = QObject::tr("提示"); //tr("提示") 全局1个,避免多次翻译 全局无法导入翻译
static const QList<QColor> gk_strColorValueList = {QColor(Qt::blue), QColor(Qt::green), QColor("#FF9900"), QColor(Qt::red)};
static const int gk_iWaitMsecs = 1000; //重发间隔 ms
static const int gk_iReSendTimes = 3;  //重发次数

static const QString gk_strTecDBConnect = "tec_connect";
static const QString gk_strHistoryDBConnect = "history_connect";
static const QString gk_strMotorInfoDBConnect = "motorinfo_connect";
static const QString gk_strSystemDBConnect = "system_connect";
static const QString gk_strLogDBConnect = "log_connect";
static const QString gk_strTimingDBConnect = "timing_connect";
static const QString gk_strMotorDBConnect = "motor_connect";
static const QString gk_strUserDBConnect = "user_connect";
static const QString gk_strLotInfoDBConnect = "lotinfo_connect";
static const QString gk_strProjectDBConnect = "project_connect";

const QString gk_strAdminUserName = "Admin";
const QString gk_strFactoryUserName = "factory";
const QString gk_strFactoryPassWord = "Wondfo2017";
const QString gk_strFlyUserName = "FLY";
const QString gk_strFlyPassword = "guangzhou.wondfo";
const QString gk_strMaintianUserName = "maintain";
const QString gk_strMaintianPassword = "Wondfo2025";

const QString gk_strAppName        = "7CAPP";
const QString gk_strAlgorithmName  = "libCCalCTLib.so";
const QString gk_strAutoName       = "auto.tar.gz";
const QString gk_strMyShName       = "mylocalconfig.sh";
const QString gk_strFirmName       = "mExtractor.xbin";
const QString gk_strSlaveName      = "kExecutor.xbin";
const QString gk_strFLName         = "mFLSensor-MPPC.xbin";
const QString gk_strPCRName        = "eThermoCycler.xbin";

#define SPLIT_BETWEEN_CMD ";"   //指令与指令之间分隔符
#define SPLIT_IN_CMD      ","   //指令内部分隔符
#define SPLIT_MOTOR       "^"    //电机串行组合指令分隔
#define SPLIT_TIMING_SYNC "&"   //时序并行指令分隔

#define COL_NUM_ID     0  //ID列号
#define COL_NUM_CMD    1  //命令列号
#define COL_NUM_PARAM1 2  //参数1列号
#define COL_NUM_PARAM2 3  //参数2列号
#define COL_NUM_PARAM3 4  //参数3列号
#define COL_NUM_PARAM4 5  //参数4列号
#define COL_NUM_CHECK  6  //CHECK列号

#define LIGHT_COLOR_FAM_INDEX 0
#define LIGHT_COLOR_HEX_INDEX 1
#define LIGHT_COLOR_ROX_INDEX 2
#define LIGHT_COLOR_CY5_INDEX 3

enum ESofType
{
    Soft_Auto = 0,   //全自动
    Soft_Extarct = 1 //提取工装
};

//语言
enum EnumLanguage
{
    eLanguage_Chinese = 0,
    eLanguage_English = 1,
    eLanguage_Spanish = 2,
    eLanguage_German  = 3,
    eLanguage_Italian = 4,
    eLanguage_Thai    = 5

};

//账号等级
enum UserLevel
{
    eUser_Normal = 0,
    eUser_Admin = 1,
    eUser_Maintain = 2,
    eUser_Factory = 3,
    eUser_Fly = 4,
};

// 网络连接模式
enum NETWORK_TYPE
{
    eNetwork_Null  = -1,  //无网络
    eNetwork_Wifi  = 0,   //WIFI
    eNetwork_Eth   = 1,   //本地网络
};

// 测试时序类型
enum TESTTEC_TYPE
{
    eTecType_Pcr = 0,
    eTecType_Hrm = 1,
    eTecType_HrmTD = 2,
};

enum ColorType
{
    B = 0,
    G = 1,
    Y = 2,
    R = 3,
};

//上位机的保存日志类型
enum LogType
{
    LOG_TYPE_DEBUG   = 0,
    LOG_TYPE_INFO    = 1,
    LOG_TYPE_WARNING = 2,
    LOG_TYPE_ERROR   = 3,
    LOG_TYPE_FATAL   = 4,
    LOG_TYPE_SLAVE   = 5, //下位机日志
    LOG_TYPE_MAX     = 6,
};
typedef struct SLogSave
{
    LogType logType;
    QString strLog;
}SLogSaveStruct;

enum FaultCode
{
    FaultCode_Order=0,    //序号
    FaultCode_ID=1,       //故障码
    FaultCode_Level=2,    //故障等级
    FaultCode_Unit=3,     //故障单元
    FaultCode_UserDesc=4, //用户描述
    FaultCode_FacDesc=5,  //工程描述
    FaultCode_Handle=6    //故障处理
};

enum EnumMethod_MotherBoard
{
    Method_start            = 1, //开始测试
    Method_stop             = 2, //停止测试
    Method_pause            = 3, //暂停时序
    Method_resume           = 4, //恢复时序
    Method_status           = 5, //仪器状态
    Method_valve_fan_on     = 6, //打开蜡阀风扇
    Method_valve_fan_off    = 7, //关闭蜡阀风扇
    Method_sys_info         = 8, //仪器信息
    Method_env_temp         = 9, //系统环境温度
    Method_ht_info          = 10, //加热模块温度
    Method_read_motor_cmd   = 11, //读取特定电机指令
    Method_set_motor_cmd    = 12, //设置特定电机指令
    Method_read_all_cmds    = 13, //读取全部电机指令
    Method_reset_all_cmds   = 14, //清空全部电机指令到出厂状态
    Method_delete_motor_cmd = 15, //删除特定电机指令
    Method_as_debug         = 16, //配置读取调试信息是否输出到串口
    Method_mlog             = 17, //中位机日志文件传输预申请
    Method_mlog_req         = 18, //日志开始指令
    Method_mlog_data        = 19, //日志数据请求指令
    Method_mlog_end         = 20, //日志结束指令
    Method_mlog_info        = 21, //日志信息
    Method_opt_byte         = 22, //设置/读取OptionByte信息
    Method_timing_file      = 23, //设置时序文件
    Method_timing_step      = 24, //当前时序运行步数
    Method_notify_flag      = 25, //设置主动上报数据包标记
    Method_rtc              = 26, //设置系统时间
    Method_upgrade_req      = 27, //升级开始指令
    Method_upgrade_data     = 28, //升级数据请求指令
    Method_upgrade_end      = 29, //升级结束指令
    Method_machine_reset    = 30, //自检
    Method_no_reset         = 31, //设置时序失败不做电机板整机复位
    Method_reboot           = 32, //重启中位机应用
    Method_notify           = 33, //报告状态
    Method_power_off        = 34, //关机
    Method_heart_beat       = 35, //心跳包
    Method_beep_flag        = 36, //设置蜂鸣器开启状态
    Method_beep_cfg         = 37, //配置蜂鸣器信息
    Method_erase_flash      = 38, //擦除外部Flash
    Method_dev_id           = 39, //读取/设置设备ID
    Method_fl_data          = 40, //荧光数据
    Method_valve_on         = 41, //泵接大气/三通阀1通电
    Method_valve_off        = 42, //泵接卡盒/三通阀1断电
    Method_pressure_on      = 43, //开启压力检测
    Method_pressure_stop    = 44, //停止压力检测
    Method_pressure_info    = 45, //压力数据
    Method_valve2_on        = 46, //卡盒接大气/三通阀2通电
    Method_valve2_off       = 47, //卡盒接泵/三通阀2断电
    Method_N1CP_N2CA        = 48, //气嘴1接泵/气嘴2接大气
    Method_N2CP_N1CA        = 49, //气嘴2接泵/气嘴1接大气
    Method_PumpConAir       = 50, //泵接大气
    Method_timing_async     = 51, //时序并行组合
    Method_parallel_start   = 52, //时序并行开始
    Method_parallel_end     = 53, //时序并行结束
    Method_serial_start     = 54, //时序串行开始
    Method_serial_end       = 55, //时序串行结束
    Method_can_id           = 56, //读取设置CAN ID
    Method_start_identify   = 57, //开启仪器识别(状态灯)
    Method_stop_identify    = 58, //停止仪器识别(状态灯)
    Method_Get_PBVesion     = 59, //获取电源小板版本
    Method_start_selftest   = 60, //进入仪器自检状态
    Method_stop_selftest    = 61, //退出仪器自检状态
};

enum EnumMethod_Motor
{
    Method_MCHK = 257, //整机自检
    Method_SRST = 258, //电机复位

    //
    Method_OFSTRST   = 259, //光学滤片电机自检复位
    Method_OFRST   = 260,   //光学滤片电机复位
    Method_OFCOMP = 262,    //光学旋转350度扫描
    Method_PCRSTRST = 263,  //PCR夹紧电机自检复位
    Method_PCRRST = 264,     //PCR夹紧电机复位
    Method_PCRPR = 265,     //PCR夹紧电机复位

    Method_TV1RST = 278, //顶针阀1复位/松开
    Method_TV1PR = 279, //顶针阀1压紧
    Method_TV2RST = 280, //顶针阀2复位/松开
    Method_NZPR = 281, //顶针阀2气嘴压紧
    Method_TV2PR = 282, //顶针阀2压紧
    Method_TV3RST = 283, //顶针阀3复位/松开
    Method_TV3PR = 284, //顶针阀3压紧
    Method_TV4RST = 285, //顶针阀4复位/松开
    Method_TV4PR = 286, //顶针阀4压紧

    Method_PM1RST = 287, //刺破电机1复位
    Method_PM1PR = 288, //刺破电机1刺破
    Method_PM2RST = 289, //刺破电机2复位
    Method_PM2PR = 290, //刺破电机2刺破
    Method_PM3RST = 291, //刺破电机3复位
    Method_PM3PR = 292, //刺破电机3刺破
    Method_PM4RST = 293, //刺破电机4复位
    Method_PM4PR = 294, //刺破电机4刺破

    Method_HXRST = 295, //提取水平压紧电机X复位
    Method_HXMTM = 296, //提取水平压紧电机X移动到混匀位
    Method_HXMTA = 297, //提取水平压紧电机X移动到磁珠吸附位
    Method_HXMTH = 298, //提取水平压紧电机X移动到加热位
    Method_HYRST = 299, //提取水平超声电机Y复位/裂解腔
    Method_HYMTE = 300, //提取水平超声电机Y移动到洗脱腔

    //    Method_NZRST = 288, //气嘴复位/松开
    //    Method_PCRVRST = 290, //PCR阀电机松开/复位
    //    Method_PCRVPR = 291, //PCR阀电机压紧
    //    Method_MRST = 292, //钢珠混匀电机复位
    //    Method_MMIX = 293, //钢珠混匀电机混匀**圈
    //    Method_VRST = 294, //提取组件升降运动电机复位/混匀位
    //    Method_VMTA = 295, //提取组件升降运动电机到吸附位
    //    Method_VMTH = 296, //提取组件升降运动电机到加热位
    //    Method_HRST = 297, //提取组件水平运动电机复位
    //    Method_HMTH = 298, //提取组件水平运动电机到加热位
    //    Method_HMTA = 299, //提取组件水平运动电机到磁珠吸附位
    //    Method_HMTM = 300, //提取组件水平运动电机到混匀位

    //
    Method_PRST = 301, //柱塞泵电机复位
    Method_PEXT = 302, //柱塞泵电机抽取**ml
    Method_PINJ = 303, //柱塞泵电机注射**ml

    Method_PETMC = 304,//柱塞泵抽取样本**ml到混合腔
    Method_PITEC = 305, //柱塞泵注射样本**ml到废液腔
    Method_PITDC = 306, //柱塞泵注射样本**ml到PCR腔/检测腔
    Method_PFEXT = 307, //泵快速抽取**ml

    Method_PRRST = 308, //柱塞泵电机逆向复位
    Method_VRST  = 310, //提取升降电机Z复位/混匀位
    Method_VMTA  = 311, //提取升降电机Z移动到吸附位
    Method_VMTH  = 312, //提取升降电机Z移动到加热位
    Method_TVPR14= 313, //顶针阀1-4压紧

    Method_PCRMRST = 345,  //PCR模块复位
    Method_PCRPRTH = 347,  //PCR加热压紧


    Method_RIGHT = 389,

    Method_GAP = 395,
    Method_SAP = 396,

    //
    Method_SCMP = 400, //设置电机补偿
    Method_GCMP = 401, //获取电机补偿
    Method_RLCMP = 402, //电机补偿恢复出厂
    Method_SDIR = 403, //设置电机复位方向
    Method_GDIR = 404, //获取电机复位方向
    Method_SPAM = 405, //设置电机参数
    Method_GPAM = 406, //获取电机参数
    Method_MOVE = 407, //走指定步数
    Method_GMSL = 408, //读取电机步长
    Method_SMSL = 409, //设置电机步长
    Method_RLPAM = 410, //电机参数恢复出厂
    Method_GCID = 411, //获取芯片ID号
    Method_OPB = 412, //optbyte
    Method_CFFL = 413, //冲突检测参数
    Method_STAT = 414, //获取电机状态(可清空寄存器异常状态)
    Method_GXIO = 415, //获取光耦状态
    Method_CCMP = 416, //电机补偿恢复出厂
    Method_ICMP = 417, //导入电机补偿
    Method_ECMP = 418, //导出电机补偿
    Method_IPAM = 419, //导入电机参数
    Method_EPAM = 420, //导出电机参数
    Method_SREGA = 421, //设置寄存器组
    Method_RREGA = 422, //读取寄存器组
    Method_CREGA = 423, //清除寄存器组
    Method_SRCHOP = 424, //设置斩波器
    Method_RRCHOP = 425, //读取斩波器
    Method_CRCHOP = 426, //清除斩波器
    Method_CLEARPOS = 427, //位置清零
    Method_ACTUALPOS = 428, //当前位置
    Method_ENN = 429, //使能所有电机
    Method_GTCOC = 430, //获取磁套检测光耦
    Method_MSWST = 431, //微动开关状态
    Method_MSWFLAG = 432, //设置微动开关检测标记位
    Method_GMCLK = 433, //读取电机时钟频率
    Method_SMCLK = 434, //设置电机时钟频率
    Method_RLCFG = 435, //电机配置恢复出厂
    Method_MOTOR_COMPOSE = 436,// 电机串行组合
    Method_Motor_CMD = 10000, //单个电机补偿 软件内部使用
};

enum EnumMethod_PCR
{
    Method_pcr_start          = 513, //启动PCR
    Method_pcr_stop           = 514, //停止PCR
    Method_pcr_tec_table_req  = 515, //请求传输TEC时序表
    Method_pcr_tec_table_data = 516, //传输TEC时序数据
    Method_pcr_tec_table_end  = 517, //传输TEC时序结束
    Method_pcr_info           = 518, //PCR运行信息
    Method_pcr_set_info_interval  = 519, //设置状态上传间隔时间
    Method_pcr_signal         = 520, //PCR信号上报
    Method_pcr_version        = 553, //版本信息
    Method_pcr_reboot         = 554, //重启
    Method_pcr_upgrade_req    = 555, //升级开始指令
    Method_pcr_upgrade_data   = 556, //升级数据请求指令
    Method_pcr_upgrade_end    = 557, //升级结束指令
    Method_pcr_save_env       = 558, //保存配置参数
    Method_pcr_set_tpid       = 583, //设置温度环PID参数
    Method_pcr_get_tpid       = 584, //获取温度环PID参数
    Method_pcr_set_ipid       = 585, //设置电流环PID参数
    Method_pcr_get_ipid       = 586, //获取电流环PID参数
    Method_pcr_set_vpid       = 587, //设置电压环PID参数
    Method_pcr_get_vpid       = 588, //获取电压环PID参数
    Method_pcr_set_treach     = 589, //设置温度到达判断阈值
    Method_pcr_cali           = 590, //设置传感器温度校准
    Method_wait_signal        = 591, //等待PCR信号
};

enum EnumMethod_Light
{
    Method_FLLED     = 769, //开启LED灯
    Method_FLADC     = 770, //获取荧光数据
    Method_FLCST     = 771, //开启连续采光
    Method_FLCDT     = 772, //连续采样数据
    Method_FLCSP     = 773, //结束连续采光
    Method_FLFREQ    = 774, //设置采集频率
    Method_FLMST     = 775, //启动运动采光
    Method_FLMSP     = 776, //结束运动采光
    Method_FLMDT     = 777, //运动采光数据
    Method_FLGAINSET = 778, //荧光校准参数设置
    Method_FLGAINGET = 779, //荧光校准参数获取

    Method_fl_upgrade_req  = 780, //荧光升级开始请求
    Method_fl_upgrade_data = 781, //荧光升级数据请求
    Method_fl_upgrade_end  = 782, //荧光升级结果
    Method_fl_version      = 783, //查询荧光版本
    Method_fl_reboot       = 784, //重启荧光
    Method_fl_ledi_get     = 785, //读取LED电流
    Method_fl_ledi_set     = 786, //设置LED电流
    Method_fl_mppcv_set    = 787, //设置MPPC偏压
    Method_fl_mppcv_get    = 788, //获取MPPC偏压
};

enum EnumMethod_Heater
{
    Method_HTST     = 1025, //启动加热
    Method_HTSP     = 1026, //停止加热
    Method_HTSET    = 1027, //设置PID参数
    Method_HTGET    = 1028, //读取PID参数
    Method_HTCALC   = 1029, //温度校准
    Method_HTPARAMS = 1030, //设置/读取PID和上报加热信息频率
};

//超声单指令命令ID
enum EnumMethod_US
{
    Method_US_USST      = 1281, //启动超声
    Method_US_USSP      = 1282, //停止超声
    Method_US_USPSET    = 1283, //设置超声功率PID参数
    Method_US_USMSET    = 1284, //设置超声电机PID参数
    Method_US_AMP       = 1285, //设置超声振幅
    Method_US_GPWR      = 1286, //获取运行状态
    Method_US_USREBOOT  = 1287, //重启超声模块/时间校正
    Method_US_USPARAM   = 1288, //超声参数
    Method_US_USFTY     = 1289, //模块厂家0：佳源达，1：东方金荣
    Method_US_USVERSION = 1290, //版本信息
    Method_US_USINFO    = 1291, //获取超声状态，频率，振幅 和 功率
    Method_US_USAMPOPT  = 1292, //从超声振幅列表选择振幅大小
    Method_US_USAMPLIST = 1293, //超声振幅列表
};


enum EnumMethod_VT
{
    Method_DELAY         = 1793, //延时
    Method_loop_st       = 1794, //循环开始
    Method_loop          = 1795, //循环
    Method_jump          = 1796, //跳转
    Method_communication = 1797, //通信检测
};

enum EnumMethod_Network
{
    Method_network_as_debug = 2305,
    Method_network_mlog,
    Method_network_mlog_req,
    Method_network_mlog_data,
    Method_network_mlog_end,
    Method_network_mlog_info,
    Method_network_rtc,
    Method_network_upgrade_req,
    Method_network_upgrade_data,
    Method_network_upgrade_end,
    Method_network_reboot,
    Method_network_power_off,
    Method_network_heart_beat,
    Method_network_beep_flag,
    Method_network_beep_cfg,
    Method_network_erase_flash,
    Method_network_pb_version,
};

struct SCanBusDataStruct
{
    quint8 quMachineID = 0x00;// 0-7
    quint8 quCmdID = 0x00;
    quint8 quObjectID = 0x00;
    quint8 quSourceID = 0x01;
    quint8 quFormat = 0x00;
    quint16 quFrameSeq = 0x00;// 帧号
    quint16 quReserve = 0x0000; //
    quint32 quMethonID = 0x00000000;
    QByteArray qbPayload = "";
};

typedef struct _STSendStruct
{
    _STSendStruct()
    {
        iMachineID = -1;
        iSendTimes = 0;
        qSendTime = QDateTime::currentDateTime();
    }

    int iMachineID;
    int iSendTimes;
    QDateTime qSendTime;
    QByteArray qSendByte;
}STSendStruct;

//项目信息
struct SLotInfoStruct
{
    QString strProjectCode; // 项目代码
    QString strProjectShowName; // 项目显示名称
    QString strProjectResultName; // 项目测试结果名称
    QString strCurveName;    // 孔道名称
    QString strCurveBRGYLine;  // 孔道显示名称

    QString strSampleType; //样本类型

    // pcr
    QString strStandardizationPCR;
    QString FlInterfereKPCR;
    QString FlInterfereBPCR;
    QString strThresholdValue;   // Ct阈值
    QString strFlThreshouldValue;    // 荧光阈值
    QString strUpliftThresholdValue;  // 抬升阈值
    QString strFirstDevThreshouldValue;  // 一阶导数阈值
    QString strPQCCutoffValue;  // 阳性质控Ct范围
    // pcrEnd

    //melting
    QString strStandardizationHRM;
    QString FlInterfereKHRM;
    QString FlInterfereBHRM;
    QString strWildTypeTmValue;   // 野生型Tm值
    QString strTempRangeValue;   //  温度区间
    QString strTmThresholdValue;   //  Tm阈值
    QString strAmplitudeThresholdValue;   //  幅度阈值
    QString strTmRange;   //  Tm区间阈值
    QString strYmThreshold;   //  Ym阈值
    QString strRmThreshold;   //  Tm阈值
    // melting End

    int iTestTime = 1080;  //常规测试时间
    int iHrmTime = 7200;   //熔解测试时间
    int iAddTime = 300;
    int iMinCycleCount = 40;
    bool bForward2CT = false;
    int iForwardBeginNumber = 0;

    void Clear()
    {
        strProjectCode.clear();
        strProjectShowName.clear();
        strProjectResultName.clear();
        strThresholdValue.clear();
        strCurveName.clear();
        strCurveBRGYLine.clear();
        strSampleType.clear();

        strFlThreshouldValue.clear();
        strUpliftThresholdValue.clear();
        strFirstDevThreshouldValue.clear();
        strPQCCutoffValue.clear();

        strStandardizationPCR.clear();
        FlInterfereKPCR.clear();
        FlInterfereBPCR.clear();
        strStandardizationHRM.clear();
        FlInterfereKHRM.clear();
        FlInterfereBHRM.clear();

        strWildTypeTmValue.clear();
        strTempRangeValue.clear();
        strTmThresholdValue.clear();
        strAmplitudeThresholdValue.clear();
        strTmRange.clear();
        strYmThreshold.clear();
        strRmThreshold.clear();

        iTestTime = 1080;
        iHrmTime = 7200;
        iAddTime = 300;
        iMinCycleCount = 40;
        bForward2CT = false;
        iForwardBeginNumber = 0;
    }
};

//卡盒信息
struct SCardInfoStruct
{
    QString strCardID;  //卡盒编号 SN
    QString strCardLot; //卡盒批次
    QString strCardMFG; //卡盒生产日期
    QString strCardEXP; //卡盒过期日期
    QString strProject; //项目
    QString strTestTime;

    void Clear()
    {
        strCardID.clear();
        strCardLot.clear();
        strCardMFG.clear();
        strCardEXP.clear();
        strProject.clear();
        strTestTime.clear();
    }
};

//样本信息
struct SSampleInfoStruct
{
    //bool bQCTest = false;

    QString strQCTestModel;
    QString strSampleID;
    int iSampleType;        //样本类型下标.数据库保存下标,兼容不同语言下的显示
    QString strSampleType;  //样本类型文字.对外,显示
    QString strSamplingDate;
    QString strName;
    QString strGender;
    QString strAge;
    QString strAgeText; //年龄显示文字
    QString strBirthday;
    QString strTelephone;
    QString strTestTime;
    QString strOperator;

    void Clear()
    {
        strQCTestModel.clear();
        strSampleID.clear();
        iSampleType = 0;
        strSampleType.clear();
        strSampleType.clear();
        strSamplingDate.clear();
        strName.clear();
        strGender.clear();
        strAge.clear();
        strAgeText.clear();
        strBirthday.clear();
        strTelephone.clear();
        strTestTime.clear();
        strOperator.clear();
    }
};

// Hrm结果计算结构体
struct stHrmResult
{
    QString strHoleName;
    QString strCtValue;
    QString strTmValue;
    int nLevel = -1;// 0、1级别
    bool bHole1Ctrl = false;
    bool bHole2Ctrl = false;
    bool bPostive = false;
    bool bHaveTm = false;
    int  nIndex = 0;
};


//设备状态
enum DeviceStatus
{
    eDeviceDisconnect   = 0, //断开
    eDeviceSelfTest     = 1, //自检
    eDeviceIdle         = 2, //空闲
    eDeviceTesting      = 3, //测试中
    eDeviceTestDone     = 4, //测试完成
    eDeviceTestStopped  = 5, //测试停止
    eDeviceTestStopping = 6, //停止中
    eDeviceTestFail     = 7, //测试失败
    eDeviceFault        = 8, //故障
    eDeviceProcessing   = 9, //倒计时结束先不停止测试,增加1分钟的处理中状态
    eDeviceReset        = 10,//复位中,上位机测试完成主动下发
    eDeviceConsoleSelfTest = 11, //上位机在自检
};

enum ETestStatus
{
    eTestRunning = 0,
    eTestStop = 1,
    eTestCancel = 2,
    eTestDone = 3,
    eTestFail = 4,
};

//测试结果
struct SResultInfoStruct
{
    int iHistoryID = -1;       //历史ID
    QString strSampleID;       //样本ID
    QString strCardID;         //试剂卡ID
    QString strProjectName;    //测试项目
    QString strTestTime;  //测试开始时间
    QString strTestEndTime;
    QString strResult;         //结果
    QString strResult_Review;  //审核结果
    QString strCTValue;        //CT值
    QString strCTValue_Review; //审核CT

    QString strCTInfo; //CT_info
    QString strCTInfo_Review; //审核CT_info

    QString strTmValue;        //熔解峰值
    QString strRmValue;        //熔解峰值
    QString strHrmResult;        //熔解靶标结果
    QString strTmValue_Review;  //熔解峰值审核结果
    QString strHrmResult_Review;  //熔解靶标审核结果

    QString strMeltingInfo; //melting_info
    QString strMeltingInfo_Review; //审核melting_info

    QString strOperator;       //用户
    QString strMode = "T";     //测试类型
    int iStatus = -1;          //状态
    QString strReview;         //是否审核 //(y:已审核,n:未审核,m:修改未保存);
    int iMachineID = -1;       //机器ID 需不需要待定
    int iTestProject = -1;        // 测试项目： pcr/hrm

    void Clear()
    {
        iHistoryID = -1;
        strSampleID.clear();
        strCardID.clear();
        strProjectName.clear();
        strTestTime.clear();
        strResult.clear();
        strResult_Review.clear();
        strCTValue.clear();
        strCTValue_Review.clear();
        strOperator.clear();
        strMode = "T";
        iStatus = -1;
        strReview.clear();
        iMachineID = -1;
        iTestProject = -1;
        strTmValue.clear();
        strRmValue.clear();
        strHrmResult.clear();
        strTmValue_Review.clear();
        strHrmResult_Review.clear();
        strCTInfo.clear();
        strCTInfo_Review.clear();
        strMeltingInfo.clear();
        strMeltingInfo_Review.clear();
    }
};

//运行状态信息
struct SRunningInfoStruct
{
    SRunningInfoStruct()
    {
        Clear();
    }
    QList<float> fAmplifyList;
    int  iTecIndex;
    bool bRunning;
    bool bFactroyTest;  //工厂创建的测试还是主页创建的测试
    int iRunTimes;
    QString strTecName;
    QString strTecData;
    QString strTimingName;
    QString strTimingData;
    QMap<int, QString> strTimingMap;

    SCardInfoStruct sCardInfo;
    SSampleInfoStruct sSampleInfo;
    SResultInfoStruct sResultInfo;

    void Clear()
    {
        iTecIndex = 0;
        bRunning = false;
        bFactroyTest = false;
        iRunTimes = 1;
        fAmplifyList.clear();
        strTecName.clear();
        strTecData.clear();
        strTimingName.clear();
        strTimingData.clear();
        strTimingMap.clear();

        sCardInfo.Clear();
        sSampleInfo.Clear();
        sResultInfo.Clear();
    }
};

struct SDevParamsStruct
{
    SDevParamsStruct()
    {
        iGroupWidth = 1684;
        iGroupHeight = 306;
        iItemWidth = 390;
        iItemHeight = 220;

        iTitleHeight = 48;
        iIndexWidth = 60;

        iNameWidth = 100;
        iValueWidth = 240;
        iLabelHeight = 27;
        iAddSpacing = 12;
        iLabelSpacing = 5;

        iActIconSize = 24;
        iActSpacing = 5;
        iActBtnWidth = 150;
        iActBtnHeight = 40;

        iBottonSpacing = 10;

        iDevImageWidth = 295;
        iDevImageHeight = 302;
    }

    int iGroupWidth;
    int iGroupHeight;
    int iItemWidth;
    int iItemHeight;

    int iTitleHeight;
    int iIndexWidth;

    int iNameWidth;
    int iValueWidth;
    int iLabelHeight;
    int iAddSpacing;
    int iLabelSpacing;

    int iActIconSize;
    int iActSpacing;
    int iActBtnWidth;
    int iActBtnHeight;

    int iBottonSpacing;

    int iDevImageWidth;
    int iDevImageHeight;
};

struct SPrintInfoStruct
{
    SPrintInfoStruct()
    {
        bReportTitle = false;
        bSampleID = true;
        bSampleType = true;
        bSamplingDate = true;
        bTestTime = true;
        bCardID = true;
        bCardLot = false;
        bCardMFG = false;
        bCardEXP = false;
        bName = true;
        bGender = false;
        bAge = false;
        bBirthday = false;
        bTelephone = false;
        bStatement = false;
        bAutoPrint = false;
        bCT = false;
        bOperator = false;
    }

    bool bReportTitle;
    bool bSampleID;
    bool bSampleType;
    bool bSamplingDate;
    bool bTestTime;
    bool bCardID;
    bool bCardLot;
    bool bCardMFG;
    bool bCardEXP;
    bool bName;
    bool bGender;
    bool bAge;
    bool bBirthday;
    bool bTelephone;
    bool bStatement;
    bool bAutoPrint;
    bool bCT;
    bool bOperator;

    QString strStatement;
    QString strReportTitle;
};

struct SPLCVerStruct
{
    QString strSN; //下位机SN

    QString strMedianShowVersion;  //读取版本
    QString strMedianAppVersion;   //软件版本
    QString strMedianHardVersion;  //硬件版本
    QString strMedianBootVersion;  //BOOT版本

    QString strPCRShowVersion;
    QString strPCRAppVersion;
    QString strPCRHardVersion;
    QString strPCRBootVersion;

    QString strFLShowVersion;
    QString strFLAppVersion;
    QString strFLHardVersion;
    QString strFLBootVersion;
};

struct SScreenShotStruct
{
    QString strName;
    QPixmap qPixMap;
};

enum ELOGINTYPE
{
    eLogUserNotExist = 0,    //用户名不存在
    eLogNoPwd = 1,           //没有输入密码
    eLogNoReEnterPwd = 2,    //没有重复输入密码
    eLogPwdDiff = 3,         //两次密码不一致
    eLogPwdInvalid = 4,      //密码无效
    eLogPWDError = 5,        //密码错误
    eLogMachineCode = 6,     //动态密码
    eLogOK = 7,              //登录成功
    eLogout = 8,             //用户退出
    eLogDisabled = 9         //用户已被禁用
};

//TEC自检参数
struct STECSelfTestParamsStruct
{
    STECSelfTestParamsStruct()
    {
        dStartTemp = 45.0;
        dMaxTemp = 95.0;
        dEndTemp = 45.0;
        dSpan = 5.0;
        dUpRate = 9.0;
        dDownRate = 6.0;
    }

    double dStartTemp; //起始温度
    double dMaxTemp;   //最大温度
    double dEndTemp;   //降温温度
    double dSpan;      //温度误差
    double dUpRate;    //升温速率
    double dDownRate;  //降温速率
};

//热裂解自检参数
struct SLysisSelfTestParamsStruct
{
    SLysisSelfTestParamsStruct()
    {
        dMaxTemp = 60.0;
        dUpRate = 0.5;
    }

    double dMaxTemp; //最大温度
    double dUpRate;  //升温速率
};

//1个气嘴的自检参数
struct SGasSelfTestParmsStruct
{
    SGasSelfTestParmsStruct()
    {
        dAddBeginTime = 0;
        dAddEndTime = 18;
        dAddMinValue = 32;
        dAddMaxValue = 42;

        dKeepBeginTime = 30;
        dKeepEndTime = 60;
        dKeepMinValue = 30;
        dKeepMaxValue = 38;

        dSubBeginTime = 60;
        dSubEndTime = 70;
        dSubMinValue = 5;
    }

    double dAddBeginTime; //加压开始时间
    double dAddEndTime;   //加压结束时间
    double dAddMinValue;  //加压最大值的范围
    double dAddMaxValue;  //加压最大值的范围

    double dKeepBeginTime; //平衡开始时间
    double dKeepEndTime;   //平衡结束时间
    double dKeepMinValue;  //平衡最大值的范围
    double dKeepMaxValue;  //平衡最大值的范围

    double dSubBeginTime;  //泄压开始时间
    double dSubEndTime;    //泄压结束时间
    double dSubMinValue;   //泄压最小值
};

//气压自检参数
struct SPressureSelfTestParamsStruct
{
    SPressureSelfTestParamsStruct()
    {
        gas2.dAddBeginTime = 70;
        gas2.dAddEndTime = 90;
        gas2.dAddMinValue = 32;
        gas2.dAddMaxValue = 40;

        gas2.dKeepBeginTime = 100;
        gas2.dKeepEndTime = 130;
        gas2.dKeepMinValue = 30;
        gas2.dKeepMaxValue = 36;

        gas2.dSubBeginTime = 130;
        gas2.dSubEndTime = 140;
        gas2.dSubMinValue = 5;
    }

    SGasSelfTestParmsStruct gas1;
    SGasSelfTestParmsStruct gas2;
};

struct SDeviceSelfTestParamsStruct
{
    STECSelfTestParamsStruct sTECStruct;
    SLysisSelfTestParamsStruct sLysisStruct;
    SPressureSelfTestParamsStruct sPressureStruct;
};

#endif // GLOBALPARAMS_H
