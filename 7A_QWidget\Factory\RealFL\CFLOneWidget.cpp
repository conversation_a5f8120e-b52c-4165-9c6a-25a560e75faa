#include "CFLOneWidget.h"
#include "qcustomplot.h"
#include "CHistoryDB.h"
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CRunTest.h"
#include "CProjectDB.h"
#include "CLotInfoDB.h"
#include "CCalculateThread.h"
#include "CReadWriteXlsxThread.h"

CFLOneWidget::CFLOneWidget(int iMachineID, QWidget *parent)
    : QWidget(parent)
    , m_iMachineID(iMachineID)
{
    m_bShow = false;
    m_bReplot = false;
    m_bHasCalcCT = false;
    m_strCurveNameList << "FAM" << "JOE" << "ROX" << "CY5";

    for(int i=0; i<gk_iHoleCount * gk_iBGYRCount; i++)
    {
        m_dFLDataMapList.push_back(QMap<double, double>());
        m_dFLStandradMapList.push_back(QMap<double, double>());
        m_dFLCrossMapList.push_back(QMap<double, double>());
        m_StandardAmplifyList.push_back(1);

        m_ErrCurvRemarkNumList.push_back(0);
    }

    _InitWidget();
    _InitLayout();
}

CFLOneWidget::~CFLOneWidget()
{

}

void CFLOneWidget::SetCardID(const QString &strCardID)
{
    m_strNewCardID = strCardID;

    bool bResult = true;
    QList<int> IndexList;
    do {
        QString strCardIDTemp = strCardID;
        QStringList strCardIdList = strCardIDTemp.split("+");
        if(strCardIdList.size() >= 2)
        {
            QString strCardIDLeft = strCardIdList.at(0);
            QString strTestTime = strCardIdList.at(1);

            if (strTestTime.length() < 14)
            {
                bResult = false;
                break;
            }

            QString year = strTestTime.left(4);
            QString month = strTestTime.mid(4, 2);
            QString day = strTestTime.mid(6, 2);
            QString hour = strTestTime.mid(8, 2);
            QString minute = strTestTime.mid(10, 2);
            QString second = strTestTime.mid(12, 2);
            strTestTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;

            QList<double> dTempList;
            SResultInfoStruct sResult;
            if(!CProjectDB::GetInstance()->GetHistoryData(strCardIDLeft,strTestTime,sResult))
            {
                bResult = false;
                break;
            }

            // 这里需要读取东西来设置当前标签
            QStringList strErrText = sResult.strTmValue_Review.split(";");
            for (int i = 0;i < strErrText.size(); i++)
            {
                int Index = gk_strErrTextList.indexOf(strErrText.at(i));
                if(Index < 0)
                {
                    Index = 0;
                }
                IndexList.push_back(Index);
            }
        }
    } while (false);  

    if(!bResult)
    {
        qDebug()<<Q_FUNC_INFO<<"FLid:"<<m_strNewCardID<<"无备注";
    }

    if(IndexList.size() >= 8)
    {
        for (int i = 0; i < m_ErrCurvRemarkNumList.size(); i++)
        {
            m_ErrCurvRemarkNumList[i] = IndexList.at(i);
        }
    }
    else
    {
        for (int i = 0; i < m_ErrCurvRemarkNumList.size(); i++)
        {
            m_ErrCurvRemarkNumList[i] = 0;
        }
    }


}


void CFLOneWidget::InitTestInfo()
{
    SRunningInfoStruct sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID);
    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sRunInfo.sResultInfo.strProjectName, sLotInfo);
    // 以后要支持 项目信息改动 所以不能只读一次
    // 每次都读一遍更合理
    m_dStandardList.clear();
    m_dFLCrossListList.clear();
    for (int i = 0; i < 8; ++i) {
        m_dStandardList.push_back(0.0);
    }
    for (int i = 0; i < 8; ++i)
    {
        QList<float> floatList;
        for (int j = 0 ; j < 4; ++j) {
            floatList.push_back(0.0);
        }
        m_dFLCrossListList.push_back(floatList);
    }

    QStringList strList = sLotInfo.strStandardizationPCR.split(";");
    // 规范化系数
    if(strList.size() >= 8)
    {
        for (int i = 0; i < 8; i++)
        {
            bool bOk = false;
            float fTemp = strList.at(i).toFloat(&bOk);
            if(bOk)
            {
                m_dStandardList[i] = fTemp;
            }
        }
    }
    // 串扰系数
    QStringList strFlInterfereList = sLotInfo.FlInterfereKPCR.split(";");
    if(strFlInterfereList.size() >= 8)
    {
        for (int i = 0; i < 8; i++)
        {
            QStringList strOneColorList = strFlInterfereList.at(i).split(",");
            if(strOneColorList.size() >= 4)
            {
                for (int j = 0 ; j < 4 ; j++)
                {
                    bool bOk = false;
                    float fTemp = strOneColorList.at(j).toFloat(&bOk);
                    if(bOk)
                    {
                        m_dFLCrossListList[i][j] = fTemp;
                    }
                }
            }
        }
    }

    qDebug()<<Q_FUNC_INFO<<QString("%1#").arg(m_iMachineID)<<"测试项目:"<<sRunInfo.sResultInfo.strProjectName<<"规范系数:"<<strList<<"串扰系数:"<<strFlInterfereList;
}


void CFLOneWidget::ClearData()
{
    m_bHasCalcCT = false;
    m_dCTInfoList.clear();
    for(int i=0; i<m_dFLDataMapList.size(); i++)
        m_dFLDataMapList[i].clear();

    if(!m_pTestCheckBox->isChecked())
        return;

    for(int i=0; i<m_pLineEditList.size(); i++)
        m_pLineEditList.at(i)->setText("");

    QVector<double> x, y;
    for(int i=0; i<m_strCurveNameList.size(); i++)
    {
        m_pCustomPlot->graph(i)->setName(m_strCurveNameList.at(i));
        m_pCustomPlot->graph(i)->setVisible(true);
        m_pCustomPlot->graph(i)->setData(x, y);
        m_pCustomPlot->legend->item(i)->setVisible(true);
    }
    m_pCustomPlot->xAxis->setRange(0, 45);
    m_pCustomPlot->yAxis->setRange(0, 2000);
    m_pCustomPlot->replot();

    m_pHoleComboBox->setCurrentIndex(0);
    m_bStandard = false;
    for(int i=0; i<m_dFLStandradMapList.size(); i++)
        m_dFLStandradMapList[i].clear();
    for(int i=0; i<m_dFLCrossMapList.size(); i++)
        m_dFLCrossMapList[i].clear();

    for(int i=0; i<m_StandardAmplifyList.size(); i++)
        m_StandardAmplifyList[i] = 1;

    for (int i = 0; i < m_ErrCurvRemarkNumList.size(); i++)
    {
        m_ErrCurvRemarkNumList[i] = 0;
    }
}

void CFLOneWidget::ParseFLCmd(const QVariant &qVarData)
{
    QString strFLString = qVarData.toString();
    QStringList strFLList = strFLString.split(SPLIT_BETWEEN_CMD);
    int size = strFLList.size();
    if(size < 2)
        return;

    QStringList strCycleTempList = strFLList.at(0).split(SPLIT_IN_CMD);
    if(strCycleTempList.size() < 2)
        return;
    double dTemp = strCycleTempList.at(1).toDouble();
    if(0 != dTemp) //熔解数据
        return;
    qDebug()<<Q_FUNC_INFO<<"全流程荧光数据:"<<strFLString;

    int iCycle = strCycleTempList.at(0).toInt();
    if(0 == iCycle)
    {
        m_pCustomPlot->xAxis->setRange(0, 45);
        m_pCustomPlot->yAxis->setRange(0, 2000);
    }

    QStringList strHole1List = strFLList.at(1).split(SPLIT_IN_CMD);
    if(5 == strHole1List.size())
    {
        for(int i=0; i<gk_iBGYRCount; i++)
        {
            QMap<double, double> &map = m_dFLDataMapList[i];
            if(map.contains(iCycle))
            {
                qDebug()<<Q_FUNC_INFO<<"fl data循环数重复:"<<iCycle;
            }
            map[iCycle] = strHole1List.at(i + 1).toDouble();
        }
    }

    if(3 == size)
    {
        QStringList strHole2List = strFLList.at(2).split(SPLIT_IN_CMD);
        if(5 == strHole2List.size())
        {
            for(int i=0; i<gk_iBGYRCount; i++)
            {
                QMap<double, double> &map = m_dFLDataMapList[i + 4];
                map[iCycle] = strHole2List.at(i + 1).toDouble();
            }
        }
    }

    m_bFLCross = _CalcFlCrossPreprocess(m_dFLDataMapList,m_dFLCrossMapList,m_dFLCrossListList);
    m_bStandard = _AlgorithmPreprocess(m_bFLCross ? m_dFLCrossMapList : m_dFLDataMapList,m_dFLStandradMapList,m_dStandardList,m_StandardAmplifyList);

    if(m_bStandard)
    {
        emit CPublicConfig::GetInstance()->SignalUpdateFLData(m_iMachineID, m_dFLStandradMapList);
    }
    else
    {
        // 发给详情的数据
        emit CPublicConfig::GetInstance()->SignalUpdateFLData(m_iMachineID, m_bFLCross ? m_dFLCrossMapList : m_dFLDataMapList);
    }

    _ShowHoleCurrentFLData(m_pHoleComboBox->currentIndex());
}

void CFLOneWidget::ParsePCRSignalCmd(const QVariant &qVarData)
{  
    QVariantList qVarList = qVarData.toList();
    if(qVarList.isEmpty())
        return;

    int iSignal = qVarList.at(0).toInt();
    switch (iSignal)
    {
    case 0:
        RUN_LOG(QString("%1#PCR运行完成").arg(m_iMachineID + 1));
        break;
    case 1:
        // PCR光学采样触发，每一轮均有此条
        break;
    case 2:
        // PCR采样循环结束，开始计算CT
        RUN_LOG(QString("%1#PCR采样循环结束").arg(m_iMachineID + 1));
        break;
    case 3:
        // HRM熔解光学采样触发，每一轮均有此条
        break;
    case 4:
        // HRM熔解光学采样结束
        // 在熔解页面处理
        break;
    case 5:

        RUN_LOG(QString("%1#PCR采样循环开始").arg(m_iMachineID + 1));
        break;
    case 6:
        RUN_LOG(QString("%1HRM采样循环开始,起始温度,间隔").arg(m_iMachineID + 1));
        break;
    default:
        break;
    }
}

void CFLOneWidget::ParseStartCmd(int iResult)
{
    Q_UNUSED(iResult);
    _CalcCurrentTestCT(iResult);

    _SaveFL2Xlsx();
}

void CFLOneWidget::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    if(m_bReplot)
    {
        m_bReplot = false;
        m_pCustomPlot->replot();
    }

    QWidget::showEvent(pEvent);
}

void CFLOneWidget::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CFLOneWidget::_SlotHoleComboBoxChanged(int iHole)
{
    _ShowHoleCurrentFLData(iHole);
}

void CFLOneWidget::_SlotErroComboBoxChanged(int iIndex)
{
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox)
        return;


    int iNum = pComboBox->property("index").toInt();

    int nIndex = m_pHoleComboBox->currentIndex()*4 + iNum;
    if(nIndex < m_ErrCurvRemarkNumList.size())
    {
        m_ErrCurvRemarkNumList[nIndex] = iIndex;
    }

    QStringList strList;
    for(const auto& item : m_ErrCurvRemarkNumList)
    {
        if(item < gk_strErrTextList.size())
        {
            int num = item <= 0 ? 0 : item;
            strList.push_back(gk_strErrTextList.at(num));
        }
    }
    _UpdateResult(strList);
}

void CFLOneWidget::_SlotCalcBtn()
{
    QString strCardID = m_strNewCardID;
    if(strCardID.isEmpty())
    {
        ShowInformation(this, tr("提示"), tr("请输入卡盒ID"));
        return;
    }
    int iHole = m_pHoleComboBox->currentIndex();
    for(int i = 0; i < m_pErrCurvRemarkList.size(); i++)
    {
        int index = iHole * 4 + i;
        if(index < m_ErrCurvRemarkNumList.size())
        {
            // 不发信号
            m_pErrCurvRemarkList.at(i)->blockSignals(true);
            m_pErrCurvRemarkList.at(i)->setCurrentIndex(m_ErrCurvRemarkNumList.at(index));
            m_pErrCurvRemarkList.at(i)->blockSignals(false);
        }
    }

    m_pTestCheckBox->setChecked(false);
    int index = m_pColorComboBox->currentIndex();
    if(index < gk_iBGYRCount)
    {
        _CalcOneColor(index);
        return;
    }


    QString strHole = QString::number(iHole);
    QStringList strColorList = gk_strColorNameList;
    QString strFLID = "";
    int iCycleCount = 0;
    QList<qreal> dAllYDataList; //所有Y的值,用以求最大最小值确定坐标范围

    bool  bFlCross = (m_dFLCrossSetParamListList.size() >= iHole*4 + 4);
    QList<QMap<double,double>> qRawDataMapList;
    if(bFlCross)
    {
        for (int j = 0;j < gk_iBGYRCount; j++)
        {
            QString strFLID = QString("%1_%2-%3").arg(strCardID).arg(iHole).arg(gk_strColorNameList.at(j));
            QList<qreal> fRawList;
            int nCycle = 0;
            CHistoryDB::GetInstance()->GetTestDataFromFLID(strFLID,nCycle,fRawList);
            QMap<double,double> OneRawDataMap;
            int indexTemp = 0;
            for (const auto& item : fRawList)
            {
                OneRawDataMap[indexTemp++] = item;
            }
            qRawDataMapList.push_back(OneRawDataMap);
        }
    }
    for(int i=0; i<gk_iBGYRCount; ++i)
    {
        strFLID = strCardID + "_" + strHole + "-" + strColorList.at(i);
        m_qRealDataSrcY.clear();
        CHistoryDB::GetInstance()->GetTestDataFromFLID(strFLID, iCycleCount, m_qRealDataSrcY);

        if(bFlCross)
        {
            QMap<double, double> dFLResultMap;
            _CalcFlCrossResult(qRawDataMapList,dFLResultMap,m_dFLCrossSetParamListList.at(iHole*4 + i),i);
            m_qRealDataSrcY = dFLResultMap.values();
        }


        QMap<double, double> FlMap;
        for (int i = 0; i < m_qRealDataSrcY.size(); ++i)
        {
            FlMap[i] = m_qRealDataSrcY[i];
        }
        // 计算Ct,只用一个数据列表

        int nTarIndex = m_pHoleComboBox->currentIndex()*4 + i;
        m_bStandardSetParan = false;
        m_qStandardY.clear();
        // 规范化打开
        if(nTarIndex < m_fStandardSetParamList.size() && m_pStandardCheckBox->isChecked())
        {
            float target = m_fStandardSetParamList.at(nTarIndex);
            m_bStandardSetParan = _StandardOneCure(m_qRealDataSrcY,m_qStandardY,target,m_StandardAmplifySetParam);
        }

        const QList<qreal>& flYList = m_bStandardSetParan ? m_qStandardY : m_qRealDataSrcY;
        // 添加入数据列表
        dAllYDataList.append(flYList);
        // 这里需要设置协议：抬升阈值;Ct阈值

        QString strConfig;
        if(_GetCalcParam(strConfig,nTarIndex))
        {
            strConfig += ";";
            strConfig += ";";
            if(CPublicConfig::GetInstance()->GetDynamicUpValue())
            {
                strConfig += "true;";
            }
            else
            {
                strConfig += "false;";
            }
            m_bStandardSetParan ? strConfig += QString("%1;").arg(m_StandardAmplifySetParam) : "";
        }
        else
        {
            strConfig = m_pLineEditList.at(i * 4 + 2)->text() + ";";
            strConfig += ";";
            strConfig += ";";
            strConfig += ";";
            if(CPublicConfig::GetInstance()->GetDynamicUpValue())
            {
                strConfig += "true;";
            }
            else
            {
                strConfig += "false;";
            }
            m_bStandardSetParan ? strConfig += QString("%1;").arg(m_StandardAmplifySetParam) : "";
        }

        if(m_pParamConfig->text().size() > 2)
        {
            strConfig = m_pParamConfig->text();
        }
        qDebug()<<Q_FUNC_INFO<<"实时荧光算法参数："<<strConfig;

        // 抬升阈值;荧光阈值;一阶导数阈值;基线起始点-基线截止点;
        m_cCtInfo.CalcCtValue(flYList,strConfig);


        QStringList strSmoothModelCTInfoList = m_cCtInfo.getSmoothModelCTInfoStringList();
        m_pResultLineEdit->setPlainText("");
        _SelectAlgorithmValue();

        //int nFitModelIndex = m_pFitModelComboBox->currentIndex();

        if(strSmoothModelCTInfoList.size() >= 3)
        {
            m_pLineEditList.at(i * 4 + 0)->setText(strSmoothModelCTInfoList.at(0));
            m_pLineEditList.at(i * 4 + 1)->setText(strSmoothModelCTInfoList.at(1));
            m_pLineEditList.at(i * 4 + 3)->setText(strSmoothModelCTInfoList.at(2));
        }


        QVector<double> x, y;
        for(int i=0; i<flYList.size(); i++)
        {
            x.push_back(i + 1);
            y.push_back(flYList.at(i));
        }
        m_pCustomPlot->graph(i)->setName(m_strCurveNameList.at(i));
        m_pCustomPlot->graph(i)->setVisible(true);
        m_pCustomPlot->legend->item(i)->setVisible(true);
        m_pCustomPlot->graph(i)->setData(x, y);
    }

    if(nullptr == m_pVerticalLin)
    {
        m_pVerticalLin = new QCPItemStraightLine(m_pCustomPlot);

        m_pVerticalLin->setPen(QPen(Qt::red));
    }
    if(nullptr == m_pHorizontalLine)
    {
        m_pHorizontalLine = new QCPItemStraightLine(m_pCustomPlot);
        m_pHorizontalLine->setPen(QPen(Qt::black));
    }
    m_pHorizontalLine->setVisible(false);
    m_pVerticalLin->setVisible(false);
    _ResetYRange(dAllYDataList);
    m_pCustomPlot->replot();
}

void CFLOneWidget::_SlotSetParamBtn()
{
    m_pCFLSetParamWidget->setParamText(m_fStandardSetParamList);

    m_pCFLSetParamWidget->raise();
    m_pCFLSetParamWidget->show();
    m_pCFLSetParamWidget->activateWindow();

}

void CFLOneWidget::_SlotBtnList()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    QString strCardID = m_strNewCardID;
    if(strCardID.isEmpty())
    {
        ShowInformation(this, tr("提示"), tr("请输入卡盒ID"));
        return;
    }

    m_pTestCheckBox->setChecked(false);

    int index = pBtn->property("index").toInt();
    float dThread = m_pLineEditList.at(4 * index)->text().toFloat();
    m_pColorComboBox->setCurrentIndex(index);
    _CalcOneColor(index);
    if(dThread > 0)
    {
        float dCTSmooth = m_cCtInfo.CalcCTFromThreadSmooth(dThread);
        _SelectAlgorithmValue();
        m_pLineEditList.at(4 * index)->setText(QString::number(dThread));
        m_pLineEditList.at(4 * index + 3)->setText(QString::number(dCTSmooth, 'f', 2));
    }
}

void CFLOneWidget::_SlotTestCheckBox(bool bCheck)
{
    if(!bCheck)
        return;

    emit SignalShowCurrentTestCardID(m_iMachineID);

    for(int i=0; i<m_pLineEditList.size(); i++)
        m_pLineEditList.at(i)->setText("");
    int size = qMin(m_pLineEditList.size(), m_dCTInfoList.size());
    for(int i=0; i<size; i++)
        m_pLineEditList.at(i)->setText(QString::number(m_dCTInfoList.at(i)));

    _ShowHoleCurrentFLData(m_pHoleComboBox->currentIndex());
}

void CFLOneWidget::_SlotStandardCheckBox(bool bCheck)
{
    Q_UNUSED(bCheck);
}

void CFLOneWidget::_SlotFitComboBoxChanged(int iFit)
{
    if(m_pTestCheckBox->isChecked())
    {
        ShowInformation(this, tr("提示"), tr("仅支持历史计算,不支持测试中的数据"));
        return;
    }

    int iColor = m_pColorComboBox->currentIndex();
    if(4 == iColor)
    {
        ShowInformation(this, tr("提示"), tr("仅支持单个计算,请选择BGYR其中一个后再次尝试"));
        return;
    }

    if(nullptr == m_pVerticalLin)
    {
        m_pVerticalLin = new QCPItemStraightLine(m_pCustomPlot);
        m_pVerticalLin->setPen(QPen(Qt::red));
    }
    if(nullptr == m_pHorizontalLine)
    {
        m_pHorizontalLine = new QCPItemStraightLine(m_pCustomPlot);
        m_pHorizontalLine->setPen(QPen(Qt::black));
    }
    m_pHorizontalLine->setVisible(false);
    m_pVerticalLin->setVisible(false);

    qDebug()<<Q_FUNC_INFO<<m_pFitComboBox->currentText()<<m_pColorComboBox->currentText();
    QStringList strGraphNameList = {"", "", "", ""};
    QList<qreal> dYList;
    if(0 == iFit)
    {
        strGraphNameList[B] = "real";
        strGraphNameList[Y] = "smooth";
        dYList.append(m_qRealDataSrcY);
        dYList.append(m_dSmoothListY);
        m_pCustomPlot->graph(B)->setData(_List2Vector(m_qRealDataSrcX), _List2Vector(m_qRealDataSrcY));
        m_pCustomPlot->graph(Y)->setData(_List2Vector(m_dSmoothListX), _List2Vector(m_dSmoothListY));
    }
    else if(1 == iFit)
    {
        strGraphNameList[B] = "real";
        strGraphNameList[Y] = "smooth";
        strGraphNameList[R] = "fit";
        dYList.append(m_qRealDataSrcY);
        dYList.append(m_dSmoothListY);
        dYList.append(m_dLMPointYList);
        m_pCustomPlot->graph(B)->setData(_List2Vector(m_qRealDataSrcX), _List2Vector(m_qRealDataSrcY));
        m_pCustomPlot->graph(Y)->setData(_List2Vector(m_dSmoothListX), _List2Vector(m_dSmoothListY));
        m_pCustomPlot->graph(R)->setData(_List2Vector(m_dLMPointXList), _List2Vector(m_dLMPointYList));
    }
    else if(2 == iFit)
    {
        strGraphNameList[G] = "baseline";
        strGraphNameList[R] = "fit";
        dYList.append(m_dBaseLineYListS);
        dYList.append(m_dLMPointYList);
        m_pCustomPlot->graph(G)->setData(_List2Vector(m_dBaseLineXListS), _List2Vector(m_dBaseLineYListS));
        m_pCustomPlot->graph(R)->setData(_List2Vector(m_dLMPointXList), _List2Vector(m_dLMPointYList));
    }
    else if(3 == iFit)
    {
        strGraphNameList[R] = "Delta";
        dYList.append(m_dDeltaYListS);
        m_pCustomPlot->graph(R)->setData(_List2Vector(m_dDeltaXListS), _List2Vector(m_dDeltaYListS));
    }

    for(int i=0; i<strGraphNameList.size(); i++)
    {
        QString strName = strGraphNameList.at(i);
        m_pCustomPlot->graph(i)->setName(strName);

        bool bShow = strName.isEmpty() ? false : true;
        m_pCustomPlot->graph(i)->setVisible(bShow);
        m_pCustomPlot->legend->item(i)->setVisible(bShow);
    }
    _ResetYRange(dYList);
    m_pCustomPlot->replot();
}

void CFLOneWidget::_SlotDataFitModelComboBoxChanged(int iFit)
{
    if(m_pTestCheckBox->isChecked())
    {
        ShowInformation(this, tr("提示"), tr("仅支持历史计算,不支持测试中的数据"));
        return;
    }

    int iColor = m_pColorComboBox->currentIndex();
    if(4 == iColor)
    {
        ShowInformation(this, tr("提示"), tr("仅支持单个计算,请选择BGYR其中一个后再次尝试"));
        return;
    }

    QString ctValue,BenchValue,threshould,basePoint,fitDegree;
    if(!m_oneColorCtInfo.isEmpty() && m_oneColorCtInfo.size() == 5)
    {
        ctValue = m_oneColorCtInfo.at(0);
        BenchValue = m_oneColorCtInfo.at(1);
        threshould =m_oneColorCtInfo.at(2);
        basePoint =  m_oneColorCtInfo.at(3);
        fitDegree =  m_oneColorCtInfo.at(4);
    }

    // 显示基线数据
    m_pResultLineEdit->setPlainText("ctValue: " + ctValue + ", threshould：" + threshould + ", bench: " +BenchValue + basePoint +fitDegree);

    if(nullptr == m_pVerticalLin)
    {
        m_pVerticalLin = new QCPItemStraightLine(m_pCustomPlot);
        m_pVerticalLin->setPen(QPen(Qt::red));
    }
    m_pVerticalLin->point1->setCoords(ctValue.toFloat(), 0); // 设置直线通过的两个点
    m_pVerticalLin->point2->setCoords(ctValue.toFloat(), 1); // 第二个点的y坐标可以是任意值，因为是垂直线
    if(nullptr == m_pHorizontalLine)
    {
        m_pHorizontalLine = new QCPItemStraightLine(m_pCustomPlot);
        m_pHorizontalLine->setPen(QPen(Qt::black));
    }
    m_pHorizontalLine->point1->setCoords(0,threshould.toFloat()); // 设置直线通过的两个点
    m_pHorizontalLine->point2->setCoords(1,threshould.toFloat()); // 第二个点的y坐标可以是任意值，因为是垂直线
    m_pHorizontalLine->setVisible(false);
    m_pVerticalLin->setVisible(false);
    qDebug()<<Q_FUNC_INFO<<m_pDataModelComboBox->currentText()<<m_pColorComboBox->currentText();
    QStringList strGraphNameList = {"", "", "", ""};

    double dMin = 200000, dMax = 0;

    QVector<double> vecX,vecY;
    auto pointfToVectorFun = [&vecX,&vecY,&dMin,&dMax](const QList<QPointF> pointList){
        vecX.clear();
        vecY.clear();

        for(auto& item : pointList)
        {
            vecX.push_back(item.x());
            vecY.push_back(item.y());
        }
        auto min_value = std::min_element(vecY.begin(), vecY.end());
        auto max_value = std::max_element(vecY.begin(), vecY.end());
        if (min_value != vecY.end()) {
            if(dMin > *min_value )
            {
                dMin = *min_value;
            }
        }
        if(dMax < *max_value)
        {
            dMax = *max_value;
        }
    };

    if(0 == iFit)
    {
        strGraphNameList[B] = "real";
        strGraphNameList[Y] = "smooth";
        strGraphNameList[R] = "base_line";

        pointfToVectorFun(m_dataListMap["real"]);
        m_pCustomPlot->graph(B)->setData(vecX, vecY);
        pointfToVectorFun(m_dataListMap["smooth"]);
        m_pCustomPlot->graph(Y)->setData(vecX, vecY);
        pointfToVectorFun(m_dataListMap["base_line"]);
        m_pCustomPlot->graph(R)->setData(vecX, vecY);
        //m_pHorizontalLine->setVisible(true);
        m_pVerticalLin->setVisible(true);
        //findMaxAndMinValue();

    }
    else if(1 == iFit)
    {
        strGraphNameList[B] = "delta";
        pointfToVectorFun(m_dataListMap["delta"]);
        m_pCustomPlot->graph(B)->setData(vecX, vecY);
        m_pHorizontalLine->setVisible(true);
        m_pVerticalLin->setVisible(true);

        strGraphNameList[G] = "delatRn4s_fitData";
        pointfToVectorFun(m_dataListMap["delatRn4s_fitData"]);
        m_pCustomPlot->graph(G)->setData(vecX, vecY);
    }

    else if(2 == iFit)
    {
        strGraphNameList[B] = "correct_data";
        pointfToVectorFun(m_dataListMap["correct_data"]);
        m_pCustomPlot->graph(B)->setData(vecX, vecY);
        m_pHorizontalLine->setVisible(true);
        m_pVerticalLin->setVisible(true);

        strGraphNameList[G] = "correct_fitData";
        pointfToVectorFun(m_dataListMap["correct_fitData"]);
        m_pCustomPlot->graph(G)->setData(vecX, vecY);
    }
    else if(3 == iFit)
    {
        strGraphNameList[Y] = "delatRn4s_firstDev";
        pointfToVectorFun(m_dataListMap["delatRn4s_firstDev"]);
        m_pCustomPlot->graph(Y)->setData(vecX, vecY);

        //m_pHorizontalLine->setVisible(true);
        m_pVerticalLin->setVisible(true);
    }
    else if(4 == iFit)
    {
        strGraphNameList[R] = "delatRn4s_SecondDev";

        pointfToVectorFun(m_dataListMap["delatRn4s_SecondDev"]);
        m_pCustomPlot->graph(R)->setData(vecX, vecY);

        //m_pHorizontalLine->setVisible(true);
        m_pVerticalLin->setVisible(true);
    }
    else if(5 == iFit)
    {
        strGraphNameList[B] = "real";
        strGraphNameList[G] = "4s_fitData";
        pointfToVectorFun(m_dataListMap["4s_fitData"]);
        m_pCustomPlot->graph(G)->setData(vecX, vecY);
        pointfToVectorFun(m_dataListMap["real"]);
        m_pCustomPlot->graph(B)->setData(vecX, vecY);
        //findMaxAndMinValue();
    }
    else if(6 == iFit)
    {
        strGraphNameList[R] = "4s_firstDev";
        pointfToVectorFun(m_dataListMap["4s_firstDev"]);
        m_pCustomPlot->graph(R)->setData(vecX, vecY);

        //m_pHorizontalLine->setVisible(true);
        m_pVerticalLin->setVisible(true);

    }
    else if(7 == iFit)
    {
        strGraphNameList[Y] = "4s_secondDev";
        pointfToVectorFun(m_dataListMap["4s_SecondDev"]);
        m_pCustomPlot->graph(Y)->setData(vecX, vecY);

        //m_pHorizontalLine->setVisible(true);
        m_pVerticalLin->setVisible(true);

    }


    for(int i=0; i<strGraphNameList.size(); i++)
    {
        QString strName = strGraphNameList.at(i);
        m_pCustomPlot->graph(i)->setName(strName);

        bool bShow = strName.isEmpty() ? false : true;
        m_pCustomPlot->graph(i)->setVisible(bShow);
        m_pCustomPlot->legend->item(i)->setVisible(bShow);
    }



    double y1 = dMax + (dMax - dMin) * 0.2;
    double y0 = dMin - (dMax - dMin) * 0.1;
    if(y0 == y1)
    {
        y0 -= 1;
        y1 += 1;
    }
    m_pCustomPlot->yAxis->setRange(y0, y1);

    m_pCustomPlot->replot();
}

void CFLOneWidget::_SlotChangedChartXYRange(const QStringList &strRangeList)
{
    if(4 != strRangeList.size())
        return;

    m_pCustomPlot->xAxis->setRange(strRangeList.at(0).toDouble(), strRangeList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strRangeList.at(2).toDouble(), strRangeList.at(3).toDouble());
    m_pCustomPlot->replot();
}

void CFLOneWidget::_SlotStandardParam(QList<float> pfList)
{
    m_fStandardSetParamList = pfList;
}

void CFLOneWidget::_SlotFLCrossParam(QList<float> pfList)
{
    for(int i = 0 ; i < m_dFLCrossSetParamListList.size() ; i++)
    {
        m_dFLCrossSetParamListList[i].clear();
    }
    m_dFLCrossSetParamListList.clear();

    if(pfList.size() >= 32)
    {
        for(int i = 0; i < 8 ; i++)
        {
            QList<float> fOneColorList;
            for(int j = 0 ; j < 4; j++)
            {
                int index = i*4 + j;
                fOneColorList.push_back(pfList.at(index));
            }
            m_dFLCrossSetParamListList.push_back(fOneColorList);
        }
    }
}

void CFLOneWidget::_UpdateResult(const QStringList& strList)
{
    // 更新备注信息
    // 在选择 通道后 插入到数据库中
    // 还是在计算的时候读出来？（否决）
    // 文本输入编辑 可改动~，  复选框；
    bool bResult = true;
    do {
        QString strCardIDTemp = m_strNewCardID;
        QStringList strCardIdList = strCardIDTemp.split("+");
        if(strCardIdList.size() >= 2)
        {
            QString strCardIDLeft = strCardIdList.at(0);
            QString strTestTime = strCardIdList.at(1);

            if (strTestTime.length() < 14)
            {
                bResult = false;
                break;
            }

            QString year = strTestTime.left(4);
            QString month = strTestTime.mid(4, 2);
            QString day = strTestTime.mid(6, 2);
            QString hour = strTestTime.mid(8, 2);
            QString minute = strTestTime.mid(10, 2);
            QString second = strTestTime.mid(12, 2);
            strTestTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;

            SResultInfoStruct sResult;
            if(!CProjectDB::GetInstance()->GetHistoryData(strCardIDLeft,strTestTime,sResult))
            {
                bResult = false;
                break;
            }
            // 这里需要读取东西来设置当前标签
            sResult.strTmValue_Review = strList.join(";");
            // 更新到TmValueReview
            CProjectDB::GetInstance()->UpdateHistoryReviewData(sResult);
        }
    } while (false);  // 条件恒为false，仅执行一次
    if(!bResult)
    {
        qDebug()<<Q_FUNC_INFO<<"FLid:"<<m_strNewCardID<<"更新备注失败:"<<strList;
    }

}

bool CFLOneWidget::_GetCalcParam(QString& strConfig, int index)
{
    // 配置，抬升阈值与Ct阈值
    //#1#-K1-FAM-5L-2+20250328154212
    bool bResult = false;
    QString strCardIDTemp = m_strNewCardID;
    QStringList strCardIdList = strCardIDTemp.split("+");
    if(strCardIdList.size() >= 2)
    {
        QString strCardIDLeft = strCardIdList.at(0);
        QString strTestTime = strCardIdList.at(1);

        if (strTestTime.length() < 14)
        {
            return bResult;
        }

        QString year = strTestTime.left(4);
        QString month = strTestTime.mid(4, 2);
        QString day = strTestTime.mid(6, 2);
        QString hour = strTestTime.mid(8, 2);
        QString minute = strTestTime.mid(10, 2);
        QString second = strTestTime.mid(12, 2);
        strTestTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;

        QList<double> dTempList;
        SResultInfoStruct sResult;
        if(!CProjectDB::GetInstance()->GetHistoryData(strCardIDLeft,strTestTime,sResult))
        {
            return bResult;
        }
        SLotInfoStruct sLotInfo;
        CLotInfoDB::GetInstance()->GetLotInfoByShowName(sResult.strProjectName, sLotInfo);
        QStringList strUpliftValueList = sLotInfo.strUpliftThresholdValue.split(";");
        QStringList strFlThreshouldList = sLotInfo.strFlThreshouldValue.split(";");
        QStringList strFirstDevValueList = sLotInfo.strFirstDevThreshouldValue.split(";");

        QStringList strConfigList = {"0","0","0"};
        if( index < strUpliftValueList.size())
        {
            strConfigList[0] = strUpliftValueList.at(index);
        }
        if( index < strFlThreshouldList.size())
        {
            strConfigList[1] = strFlThreshouldList.at(index);
        }
        if( index < strFirstDevValueList.size())
        {
            strConfigList[2] = strFirstDevValueList.at(index);
        }
        strConfig = strConfigList.join(";");
        bResult = true;
    }
    else
    {
        bResult = false;
    }
    return bResult;
}


void CFLOneWidget::_CalcCurrentTestCT(int iResult)
{
    if(m_bHasCalcCT) //防止重复计算,有2种情况计算CT: (1)收到start (2)收到PCR循环采样结束
        return;

    m_bHasCalcCT = true;
    SRunningInfoStruct sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID);
    sRunInfo.sResultInfo.iStatus = (0 == iResult)? eTestDone :eTestFail;
    if(m_bStandard)
    {
        sRunInfo.fAmplifyList = m_StandardAmplifyList;
        CCalculateThread::GetInstance()->calculateHandle(sRunInfo,m_dFLStandradMapList);
    }
    else
    {
        sRunInfo.fAmplifyList.clear();
        CCalculateThread::GetInstance()->calculateHandle(sRunInfo,m_bFLCross ? m_dFLCrossMapList : m_dFLDataMapList);
    }
    qDebug()<<Q_FUNC_INFO<<"开始计算:"<<m_iMachineID<<sRunInfo.sResultInfo.iHistoryID<<"测试状态:"<<iResult;

    // 写数据库  一个串扰 一个规范化
    // 后面读取原始数据 和  计算 串扰数据的时候需要用到;
    int nSizeTemp = qMin(m_dFLDataMapList.size(),m_dFLStandradMapList.size());
    int nSize = qMin(nSizeTemp,m_dFLCrossMapList.size());
    for(int i=0; i < nSize; i++)
    {
        QList<double> dRawFLList = m_dFLDataMapList.at(i).values();
        QList<double> dStandardFLList = m_dFLStandradMapList.at(i).values();;
        QList<double> dCrossFLList = m_dFLCrossMapList.at(i).values();;
        QString strHole = QString::number(i / 4);
        QString strColor = gk_strColorNameList.at(i % 4);
        QString strTestTime = sRunInfo.sCardInfo.strTestTime;
        strTestTime.remove(":").remove("-").remove(" ");
        QString strFLID = sRunInfo.sCardInfo.strCardID + "+" + strTestTime + "_" + strHole + "-" + strColor;

        QString strCurrentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        if(sRunInfo.sCardInfo.strCardID.isEmpty())
            sRunInfo.sCardInfo.strCardID = strCurrentTime;
        CHistoryDB::GetInstance()->AddTestData(strFLID, "", sRunInfo.sResultInfo.strTestTime,
                                               strCurrentTime, dRawFLList.size(),dRawFLList,dStandardFLList,dCrossFLList,"");
    }
    return;
}


void CFLOneWidget::_ShowHoleCurrentFLData(int iHole)
{
    if(!m_pTestCheckBox->isChecked()) //是否勾选测试数据按钮
        return;

    QList<double> dAllYDataList;
    for(int i=0; i<gk_iBGYRCount; i++)
    {
        m_pCustomPlot->graph(i)->setName(m_strCurveNameList.at(i));
        m_pCustomPlot->graph(i)->setVisible(true);
        m_pCustomPlot->legend->item(i)->setVisible(true);

        const QList<QMap<double, double>>& qMapList = m_bStandard ? m_dFLStandradMapList : m_dFLDataMapList;

        const QMap<double, double> &map = qMapList[i + iHole * 4];

        QVector<double> x = map.keys().toVector();
        QVector<double> y = map.values().toVector();
        m_pCustomPlot->graph(i)->setData(x, y);

        dAllYDataList.append(map.values());
    }

    _ResetYRange(dAllYDataList);

    m_bReplot = true;
    if(m_bShow)
    {
        m_bReplot = false;
        m_pCustomPlot->replot();
    }
}
void CFLOneWidget::_ClearEditCtInfoValue()
{
    // 不清除抬升阈值
    for(int i=0; i<m_pLineEditList.size(); i++)
    {
        if( i%4 != 2)
        {
            m_pLineEditList.at(i)->setText("");
        }
    }
}
void CFLOneWidget::_CalcOneColor(int index)
{
    if(index >= 4)
    {
        return;
    }
    _ClearEditCtInfoValue();
    QString strCardID = m_strNewCardID;
    int iHole = m_pHoleComboBox->currentIndex();
    QString strColorIndex = gk_strColorNameList.at(index);
    QString strFLID = QString("%1_%2-%3").arg(strCardID).arg(iHole).arg(strColorIndex);


    int iCycleCount = 0;
    m_qRealDataSrcY.clear();
    CHistoryDB::GetInstance()->GetTestDataFromFLID(strFLID, iCycleCount, m_qRealDataSrcY);
    qDebug()<<Q_FUNC_INFO<<strFLID<<iCycleCount<<m_qRealDataSrcY;
    if(m_qRealDataSrcY.count() < 1)
        return;

    if((iHole*4 + index) < m_dFLCrossSetParamListList.size())
    {
        QList<QMap<double,double>> qRawDataMapList;
        for (int j = 0;j < gk_iBGYRCount; j++)
        {
            QString strFLID = QString("%1_%2-%3").arg(strCardID).arg(iHole).arg(gk_strColorNameList.at(j));
            QList<qreal> fRawList;
            int nCycle = 0;
            CHistoryDB::GetInstance()->GetTestDataFromFLID(strFLID,nCycle,fRawList);
            QMap<double,double> OneRawDataMap;
            int indexTemp = 0;
            for (const auto& item : fRawList)
            {
                OneRawDataMap[indexTemp++] = item;
            }
            qRawDataMapList.push_back(OneRawDataMap);
        }
        QMap<double, double> dFLResultMap;
        _CalcFlCrossResult(qRawDataMapList,dFLResultMap,m_dFLCrossSetParamListList.at(iHole*4 + index),index);
        m_qRealDataSrcY = dFLResultMap.values();
    }



    // 计算Ct,只用一个数据列表
    int nTarIndex = m_pHoleComboBox->currentIndex()*4 + index;
    m_bStandardSetParan = false;
    m_qStandardY.clear();
    if(nTarIndex < m_fStandardSetParamList.size() &&  m_pStandardCheckBox->isChecked())
    {
        float target = m_fStandardSetParamList.at(nTarIndex);
        m_bStandardSetParan = _StandardOneCure(m_qRealDataSrcY,m_qStandardY,target,m_StandardAmplifySetParam);
    }

    const QList<qreal>& flYList = m_bStandardSetParan ? m_qStandardY : m_qRealDataSrcY;


    QString strConfig;
    if(_GetCalcParam(strConfig,nTarIndex))
    {
        strConfig += ";";
        strConfig += ";";
        if(CPublicConfig::GetInstance()->GetDynamicUpValue())
        {
            strConfig += "true;";
        }
        else
        {
            strConfig += "false;";
        }
        m_bStandardSetParan ? strConfig += QString("%1;").arg(m_StandardAmplifySetParam) : "";
    }
    else
    {
        strConfig = m_pLineEditList.at(index * 4 + 2)->text() + ";";
        strConfig += ";";
        strConfig += ";";
        strConfig += ";";
        if(CPublicConfig::GetInstance()->GetDynamicUpValue())
        {
            strConfig += "true;";
        }
        else
        {
            strConfig += "false;";
        }
        m_bStandardSetParan ? strConfig += QString("%1;").arg(m_StandardAmplifySetParam) : "";
    }

    if(m_pParamConfig->text().size() > 2)
    {
        strConfig = m_pParamConfig->text();
    }
    qDebug()<<Q_FUNC_INFO<<"实时荧光算法参数："<<strConfig;

    m_cCtInfo.CalcCtValue(flYList,strConfig);

    // 取所有需要的值，按照协议取

    QStringList strSmoothModelCTInfoList = m_cCtInfo.getSmoothModelCTInfoStringList();
    _SelectAlgorithmValue();

    QString ctValue,BenchValue,threshould,basePoint,fitDegree;
    if(!m_oneColorCtInfo.isEmpty() && m_oneColorCtInfo.size() == 5)
    {
        ctValue = m_oneColorCtInfo.at(0);
        BenchValue = m_oneColorCtInfo.at(1);
        threshould =m_oneColorCtInfo.at(2);
        basePoint =  m_oneColorCtInfo.at(3);
        fitDegree =  m_oneColorCtInfo.at(4);
    }

    // 显示基线数据
    m_pResultLineEdit->setPlainText("ctValue: " + ctValue + ", threshould：" + threshould + ", bench: " +BenchValue + basePoint +fitDegree);

    qDebug()<<Q_FUNC_INFO<<index<<"get ct"<<strSmoothModelCTInfoList<<m_qRealDataSrcY.count();

    if(strSmoothModelCTInfoList.size() >= 3)
    {
        m_pLineEditList.at(index * 4 + 0)->setText(strSmoothModelCTInfoList.at(0));
        m_pLineEditList.at(index * 4 + 1)->setText(strSmoothModelCTInfoList.at(1));
        m_pLineEditList.at(index * 4 + 3)->setText(strSmoothModelCTInfoList.at(2));
    }

    _UpdateOneCurve(index, flYList);

    m_qRealDataSrcX.clear();
    for(int i = 0; i < flYList.count(); ++ i)
    {
        m_qRealDataSrcX.push_back(i + 1);
    }
}

void CFLOneWidget::_UpdateOneCurve(int index, const QList<qreal> &qDataList)
{
    if(index < 0 || index >= gk_iBGYRCount)
        return;

    for(int i=0; i<gk_iBGYRCount; i++)
    {
        bool bVisible = (i == index ? true : false);
        m_pCustomPlot->graph(i)->setVisible(bVisible);
        m_pCustomPlot->legend->item(i)->setVisible(bVisible);
    }
    m_pCustomPlot->graph(index)->setName(m_strCurveNameList.at(index));

    _ResetYRange(qDataList);

    QVector<double> x, y;
    for(int i=0; i<qDataList.size(); i++)
    {
        x.push_back(i + 1);
        y.push_back(qDataList.at(i));
    }
    if(nullptr == m_pVerticalLin)
    {
        m_pVerticalLin = new QCPItemStraightLine(m_pCustomPlot);

        m_pVerticalLin->setPen(QPen(Qt::red));
    }
    if(nullptr == m_pHorizontalLine)
    {
        m_pHorizontalLine = new QCPItemStraightLine(m_pCustomPlot);
        m_pHorizontalLine->setPen(QPen(Qt::black));
    }
    m_pHorizontalLine->setVisible(false);
    m_pVerticalLin->setVisible(false);
    m_pCustomPlot->graph(index)->setData(x, y);
    m_pCustomPlot->replot();
}

void CFLOneWidget::_SelectAlgorithmValue()
{
    m_dataListMap.clear();
    QList<QPointF> temp = m_cCtInfo.getBaseLinePointFList();

    QList<qreal>& qFLList = m_bStandardSetParan ? m_qStandardY : m_qRealDataSrcY;
    QList<QPointF> pPointList;
    for(int i = 0; i < qFLList.count(); ++ i)
    {
        pPointList.push_back(QPointF((i + 1),qFLList.at(i)));
    }
    m_dataListMap.insert("real",pPointList);
    m_dataListMap.insert("base_line",temp);

    m_dataListMap.insert("smooth",m_cCtInfo.getSmoothPointFList());
    m_dataListMap.insert("delta",m_cCtInfo.getDeltaPointFList());

    m_dataListMap.insert("4s_fitData",m_cCtInfo.getSigmoidFitPointFList(CCalCTLib::fitModelEm::kFourParamsSigmoid));
    m_dataListMap.insert("4s_firstDev", m_cCtInfo.getFirstDevPointFList(CCalCTLib::fitModelEm::kFourParamsSigmoid));
    m_dataListMap.insert("4s_SecondDev",m_cCtInfo.getSecondDevPointFList(CCalCTLib::fitModelEm::kFourParamsSigmoid));

    m_dataListMap.insert("delatRn4s_fitData",m_cCtInfo.getSigmoidFitPointFList(CCalCTLib::fitModelEm::kDelatRnSigmoid));
    m_dataListMap.insert("delatRn4s_firstDev",m_cCtInfo.getFirstDevPointFList(CCalCTLib::fitModelEm::kDelatRnSigmoid));
    m_dataListMap.insert("delatRn4s_SecondDev",m_cCtInfo.getSecondDevPointFList(CCalCTLib::fitModelEm::kDelatRnSigmoid));

    m_dataListMap.insert("correct_data",m_cCtInfo.getDeltaPointFList());
    m_dataListMap.insert("correct_fitData", m_cCtInfo.getDeltaRnCorrectPointFList());

    m_oneColorCtInfo.clear();
    m_oneColorCtInfo = m_cCtInfo.getCtValueInfoStringList();
}
bool CFLOneWidget::_AlgorithmPreprocess(const QList<QMap<double, double>>& dFLRawMapList,QList<QMap<double, double>>& dFLStandMapList,const QList<float>& standardTarget,QList<float>& fAmplify)
{
    bool bResut = false;
    if(standardTarget.size() >= 8)
    {
        // 进行放大规范化
        int nSizeTemp = qMin(dFLStandMapList.size(),dFLRawMapList.size());
        int nSize = qMin(fAmplify.size(),nSizeTemp);
        for(int i = 0 ; i < nSize ; i++)
        {
            const auto& map = dFLRawMapList.at(i);
            QMap<double, double> mapResult;
            double target = standardTarget.at(i);
            if(target <= 0.1)
            {
                bResut = false;
                break;
            }
            double sum = 0.0;
            int count = 0;
            float factorTen = 0.0;
            for (auto it = map.begin(); it != map.end(); ++it)
            {
                double currentValue = it.value();
                sum += currentValue;

                if(count > 0)
                {
                    double factor = target / (sum/(count+1));
                    if(count < 10)
                    {
                        factorTen = factor;
                    }
                    double scaledValue = currentValue * factorTen;
                    mapResult[count] = scaledValue;
                }
                else
                {
                    double factor = target / currentValue;
                    double scaledValue = currentValue * factor;
                    mapResult[count] = scaledValue;
                }
                count++;
            }
            dFLStandMapList[i] = mapResult;
            fAmplify[i] = factorTen;
            bResut = true;
        }
    }

    //qDebug()<<Q_FUNC_INFO<<QString("%1#").arg(m_iMachineID)<<"是否规范化: "<<bResut<<"原始数据:"<<dFLRawMapList<<"规范化数据:"<<dFLStandMapList;
    qDebug()<<Q_FUNC_INFO<<QString("%1#").arg(m_iMachineID)<<"是否规范化: "<<bResut;
    return  bResut;
    // 放大系数 规范化系数
    // 荧光串扰系数 原始数据
    // 传出 处理过后的map 规范化的map, 串扰后的map
    //  串扰数据要用最原始的数据来计算， 串扰会在规范化之前；
}

bool CFLOneWidget::_CalcFlCrossPreprocess(const QList<QMap<double, double> > &dFLRawMapList, QList<QMap<double, double> > &dFLCrossMapList, const QList<QList<float> > &FLCrossListList)
{
    bool bResut = false;
    // 串扰系数
    if(FLCrossListList.size() >= 8 && 1)
    {
        // 如何运算？有八组数据，多个系数；
        for(int i = 0 ; i < 8; i++)
        {
            QMap<double,double> qCrossMap;
            if(i < 4)
            {
                _CalcFlCrossResult(dFLRawMapList.mid(0, 4),qCrossMap,FLCrossListList.at(i),i);
            }
            else
            {
                _CalcFlCrossResult(dFLRawMapList.mid(4, 4),qCrossMap,FLCrossListList.at(i),i%4);
            }
            dFLCrossMapList[i] = qCrossMap;
        }
        bResut = true;
    }
    qDebug()<<Q_FUNC_INFO<<QString("%1#").arg(m_iMachineID)<<"是否使用串扰系数: "<<bResut<< "串扰系数";
    //qDebug()<<Q_FUNC_INFO<<QString("%1#").arg(m_iMachineID)<<"是否使用串扰系数: "<<bResut<< "串扰系数" << FLCrossListList<<"原始数据:"<<dFLRawMapList<<"串扰计算后:"<<dFLCrossMapList;;
    return  bResut;
}

void CFLOneWidget::_CalcFlCrossResult(const QList<QMap<double, double>> &dFLRawList, QMap<double, double> &dFLResultMap, const QList<float>& fCrossList,int index)
{
    // 果 =  因变量 + 因变量*fCross + 因变量*fCross + 因变量*fCross + 因变量*fCross;
    // 背景荧光串扰系数 减小
    if(dFLRawList.size() >= 4 && index < 4)
    {
        int iMinCount = qMin(dFLRawList[0].count(),dFLRawList[1].count());
        iMinCount =qMin(iMinCount,dFLRawList[2].count());
        iMinCount =qMin(iMinCount,dFLRawList[3].count());
        // 遍历map
        const QMap<double,double>& oneMap  = dFLRawList[index];
        for (auto it = oneMap.begin(); it != oneMap.end(); ++it)
        {
            //qDebug() << "Key: " << it.key() << ", Value: " << it.value();
            dFLResultMap[it.key()] = it.value() - dFLRawList[0][it.key()] * fCrossList.at(0) -  dFLRawList[1][it.key()] * fCrossList.at(1) - dFLRawList[2][it.key()] * fCrossList.at(2) - dFLRawList[3][it.key()] * fCrossList.at(3);
        }
    }
}

bool CFLOneWidget::_StandardOneCure(const QList<double> &dFLRawList, QList<double> &dFLStandList, float standardTarget, float &fAmplify)
{
    bool bResult = false;
    if(standardTarget <= 0.1)
    {
        bResult = false;
        return bResult;
    }
    dFLStandList.clear();
    dFLStandList.reserve(dFLRawList.size());
    // 进行放大规范化
    double sum = 0.0;
    float factorTen = 0.0;
    for(int i = 0 ; i < dFLRawList.size() ; i++)
    {
        double currentValue = dFLRawList.at(i);
        sum += currentValue;
        if(i  < 1)
        {
            double factor = standardTarget / (sum/(i + 1));
            double scaledValue = currentValue * factor;
            dFLStandList.push_back(scaledValue);
        }
        else
        {
            if(i  < 10)
            {
                factorTen = standardTarget / (sum/(i + 1));
            }
            double scaledValue = currentValue * factorTen;
            dFLStandList.push_back(scaledValue);
        }
    }
    fAmplify = factorTen;
    bResult = true;
    return bResult;
}

QVector<double> CFLOneWidget::_List2Vector(const QList<qreal> &qDataList)
{
    QVector<double> dVec;
    for(int i=0; i<qDataList.size(); i++)
        dVec.push_back(qDataList.at(i));
    return dVec;
}

void CFLOneWidget::_ResetYRange(const QList<qreal> &qYDataList)
{
    double dMin = 0, dMax = 2000;
    GetListMinMaxValue(qYDataList, dMin, dMax);
    double y1 = dMax + (dMax - dMin) * 0.2;
    double y0 = dMin - (dMax - dMin) * 0.1;
    if(y0 == y1)
    {
        y0 -= 1;
        y1 += 1;
    }
    m_pCustomPlot->yAxis->setRange(y0, y1);

    //m_pCustomPlot->yAxis->setRange(dMin - 10, dMax + 200);
}

void CFLOneWidget::_SaveFL2Xlsx()
{
    QString strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(m_iMachineID);
    if(strXlsxName.isEmpty())
        return;

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;

    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "fl_data";
    pXlsxStruct->bDrawChart = true;

    pXlsxStruct->strTitleList<<"Cycle"<<"B1"<<"G1"<<"Y1"<<"R1"<<"B2"<<"G2"<<"Y2"<<"R2";

    int iCycle = 0;
    if(!m_dFLDataMapList.isEmpty())
        iCycle = m_dFLDataMapList.at(0).size();
    for(int i=0; i<iCycle; i++)
    {
        QVariantList qRowList;
        qRowList<<i+1;

        for(int j=0; j<m_dFLDataMapList.size(); j++)
            qRowList<<m_dFLDataMapList.at(j).value(i, 0);

        pXlsxStruct->varWriteDataList<<qRowList;
    }

    QSize qChartSize;
    qChartSize.setWidth(580);
    qChartSize.setHeight(355);

    ChartNoteStruct chart1;
    chart1.iRow = 1;
    chart1.iColumn = 10;
    chart1.qSize = qChartSize;
    chart1.strChartTitle = "孔1";
    chart1.strXTitle = "cycles";
    chart1.strYTitle = "fl";
    chart1.strMarkSymbolList << "none" << "none" << "none" << "none";
    chart1.strSerialNameList<<"B1"<<"G1"<<"Y1"<<"R1";
    chart1.strSerialColorList<<HEX_COLOR_B<<HEX_COLOR_G<<HEX_COLOR_Y<<HEX_COLOR_R;
    chart1.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(iCycle + 1);
    chart1.strNumDataRange = QString("B2:E%1").arg(iCycle + 1);

    ChartNoteStruct chart2 = chart1;
    chart2.iRow = 19;
    chart2.strChartTitle = "孔2";
    chart2.strSerialNameList.clear();
    chart2.strSerialNameList<<"B2"<<"G2"<<"Y2"<<"R2";
    chart2.strNumDataRange = QString("F2:I%1").arg(iCycle + 1);

    pXlsxStruct->chartNoteList<<chart1<<chart2;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);

    qDebug()<<QString("%1# fl data write to result xlsx end").arg(m_iMachineID + 1);
}

void CFLOneWidget::_InitCustomPlot()
{
    m_pCustomPlot = new QCustomPlot;

    QFont font;
    font.setPointSize(10);
    m_pCustomPlot->legend->setFont(font);
    m_pCustomPlot->legend->setSelectedFont(font);
    m_pCustomPlot->legend->setVisible(true);
    m_pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pCustomPlot->legend->setWrap(1);
    m_pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    QList<QColor> colorList = gk_strColorValueList;
    _AddGraph(m_pCustomPlot, colorList[B], colorList[B], B, m_strCurveNameList[B]);
    _AddGraph(m_pCustomPlot, colorList[G], colorList[G], G, m_strCurveNameList[G]);
    _AddGraph(m_pCustomPlot, colorList[Y], colorList[Y], Y, m_strCurveNameList[Y]);
    _AddGraph(m_pCustomPlot, colorList[R], colorList[R], R, m_strCurveNameList[R]);

    m_pCustomPlot->xAxis->setLabelFont(font);
    m_pCustomPlot->yAxis->setLabelFont(font);

    m_pCustomPlot->xAxis->setRange(0, 45);
    m_pCustomPlot->xAxis->ticker()->setTickCount(9);
    m_pCustomPlot->xAxis->setSubTicks(false);
    m_pCustomPlot->yAxis->setRange(0, 2000);
    m_pCustomPlot->yAxis->ticker()->setTickCount(9);
    m_pCustomPlot->yAxis->setSubTicks(false);
}

void CFLOneWidget::_AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName)
{
    QPen pen;
    pen.setWidth(2);
    pen.setColor(penColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    pCustomPlot->graph(iChart)->setScatterStyle(
                QCPScatterStyle(QCPScatterStyle::ssNone,
                                QPen(pointColor, 2),
                                QBrush(pointColor), 2));
}

void CFLOneWidget::_InitWidget()
{
    int iHeight = 35;

    m_pHoleComboBox = new QComboBox;
    m_pHoleComboBox->setFixedSize(120, iHeight);
    m_pHoleComboBox->setView(new QListView);
    m_pHoleComboBox->addItems(CPublicConfig::GetInstance()->GetHoleNameList());
    connect(m_pHoleComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotHoleComboBoxChanged(int)));

    QStringList strColorNameList = gk_strColorNameList;
    strColorNameList.push_back("All");
    m_pColorComboBox = new QComboBox;
    m_pColorComboBox->setFixedSize(120, iHeight);
    m_pColorComboBox->setView(new QListView);
    m_pColorComboBox->addItems(strColorNameList);

    m_pCalcBtn = new QPushButton(tr("计算"));
    m_pCalcBtn->setFixedSize(120, iHeight);
    connect(m_pCalcBtn, &QPushButton::clicked, this, &CFLOneWidget::_SlotCalcBtn);

    m_pResultLineEdit = new QPlainTextEdit();
    m_pResultLineEdit->setFixedSize(200,iHeight*2);


    QStringList strLabeNameList = {tr("参考阈值"), tr("抬升值"),tr("抬升比例"),tr("CT值"),tr("异常备注")};
    for(int i=0; i<strLabeNameList.size(); i++)
    {
        QLabel *pLabel = new QLabel(strLabeNameList.at(i));
        pLabel->setFixedHeight(iHeight);
        m_pLabelList.push_back(pLabel);
    }

    for(int i=0; i < 16; i++)
    {
        CLineEdit *pLineEdit = new CLineEdit;
        pLineEdit->setAlignment(Qt::AlignCenter);
        pLineEdit->setInputMethodHints(Qt::ImhDigitsOnly);
        // pLineEdit->setPlaceholderText(QString::number(i));
        pLineEdit->setFixedSize(100, iHeight);
        m_pLineEditList.push_back(pLineEdit);
    }

    for(int i=0; i<4; i++)
    {
        QPushButton *pBtn = new QPushButton(tr("计算"));
        pBtn->setFixedSize(100, iHeight);
        pBtn->setProperty("index", i);
        connect(pBtn, &QPushButton::clicked, this, &CFLOneWidget::_SlotBtnList);
        m_pBtnList.push_back(pBtn);
    }
    for (int i = 0 ; i < gk_iBGYRCount; i++)
    {      
        QComboBox * pComboBox = new QComboBox;
        pComboBox->setFixedSize(120, iHeight);
        pComboBox->setView(new QListView);
        pComboBox->setProperty("index",i);
        pComboBox->addItems(gk_strErrTextList);
        connect(pComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotErroComboBoxChanged(int)));
        m_pErrCurvRemarkList.push_back(pComboBox);
    }


    QStringList strFitList = {"Smooth", "Fit", "BaseLine", "Delta"};
    m_pFitComboBox = new QComboBox;
    m_pFitComboBox->setFixedSize(120, iHeight);
    m_pFitComboBox->setView(new QListView);
    m_pFitComboBox->addItems(strFitList);
    connect(m_pFitComboBox, SIGNAL(activated(int)), this, SLOT(_SlotFitComboBoxChanged(int)));


    QStringList strFitListTem = {"Smooth","Delta","modifyCurve", "DelatRn_firstDev","DelatRn_secondDev","4sFit","4s_firstDev","4s_secondDev"};
    m_pDataModelComboBox = new QComboBox;
    m_pDataModelComboBox->setFixedSize(120, iHeight);
    m_pDataModelComboBox->setView(new QListView);
    m_pDataModelComboBox->addItems(strFitListTem);
    connect(m_pDataModelComboBox, SIGNAL(activated(int)), this, SLOT(_SlotDataFitModelComboBoxChanged(int)));


    m_pEnlargeNum  = new CLineEdit();
    m_pEnlargeNum->setAlignment(Qt::AlignCenter);
    m_pEnlargeNum->setInputMethodHints(Qt::ImhDigitsOnly);
    m_pEnlargeNum->setPlaceholderText(QString::number(1));
    m_pEnlargeNum->setFixedSize(100, iHeight);

    m_pParamConfig  = new CLineEdit();
    m_pParamConfig->setAlignment(Qt::AlignCenter);
    m_pParamConfig->setPlaceholderText("");
    m_pParamConfig->setFixedSize(170, iHeight);


    m_pTestCheckBox = new QCheckBox(tr("Test"));
    m_pTestCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pTestCheckBox->setChecked(true);
    m_pTestCheckBox->setFixedSize(120, iHeight + 10);
    connect(m_pTestCheckBox, &QCheckBox::clicked, this, &CFLOneWidget::_SlotTestCheckBox);

    m_pStandardCheckBox = new QCheckBox(tr("Standard"));
    m_pStandardCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pStandardCheckBox->setChecked(false);
    m_pStandardCheckBox->setFixedSize(140, iHeight + 10);
    connect(m_pStandardCheckBox, &QCheckBox::clicked, this, &CFLOneWidget::_SlotStandardCheckBox);

    /*
    m_pCrossBGLineEdit = new CLabelLineEdit(tr("BG:"), "0");
    m_pCrossBGLineEdit->SetLineEditFixedSize(70, iHeight);
    m_pCrossBGLineEdit->setFixedSize(120, iHeight);
    m_pCrossBGLineEdit->setInputMethodHints(Qt::ImhDigitsOnly);
    connect(m_pCrossBGLineEdit, &CLabelLineEdit::SignalTextChanged, this, &CFLOneWidget::_SlotCrossBGChanged);

    m_pCrossGYLineEdit = new CLabelLineEdit(tr("GY:"), "0");
    m_pCrossGYLineEdit->SetLineEditFixedSize(70, iHeight);
    m_pCrossGYLineEdit->setFixedSize(120, iHeight);
    m_pCrossGYLineEdit->setInputMethodHints(Qt::ImhDigitsOnly);
    connect(m_pCrossGYLineEdit, &CLabelLineEdit::SignalTextChanged, this, &CFLOneWidget::_SlotCrossGYChanged);
     */
    m_pCFLSetParamWidget = new CFLSetParamWidget;
    connect(m_pCFLSetParamWidget, &CFLSetParamWidget::SignalStandardSetParamConfirm, this, &CFLOneWidget::_SlotStandardParam);
    connect(m_pCFLSetParamWidget, &CFLSetParamWidget::SignalFLCrossSetParamConfirm, this, &CFLOneWidget::_SlotFLCrossParam);

    m_pSetParamBtn = new QPushButton(tr("设置参数"));
    m_pSetParamBtn->setFixedSize(120, iHeight);
    connect(m_pSetParamBtn, &QPushButton::clicked, this, &CFLOneWidget::_SlotSetParamBtn);

    _InitCustomPlot();

    QStringList strRangeList = {"0", "45", "0", "2000"};
    m_pSetXY = new CSetChartXYRange(strRangeList);
    m_pSetXY->SetLineEditTextAlignment();
    m_pSetXY->SetObjFixedHeight(iHeight);
    connect(m_pSetXY, &CSetChartXYRange::SignalSetRange, this, &CFLOneWidget::_SlotChangedChartXYRange);
}

void CFLOneWidget::_InitLayout()
{
    QGridLayout *pLeftGridLayout = new QGridLayout;
    pLeftGridLayout->setMargin(0);
    pLeftGridLayout->setVerticalSpacing(5);
    pLeftGridLayout->setHorizontalSpacing(10);
    pLeftGridLayout->addWidget(m_pLabelList.at(0), 0, 0);
    pLeftGridLayout->addWidget(m_pLabelList.at(1), 1, 0);
    pLeftGridLayout->addWidget(m_pLabelList.at(2), 2, 0);
    pLeftGridLayout->addWidget(m_pLabelList.at(3), 3, 0);
    pLeftGridLayout->addWidget(m_pLabelList.at(4), 5, 0);


    pLeftGridLayout->addWidget(m_pLineEditList.at(0), 0, 1);
    pLeftGridLayout->addWidget(m_pLineEditList.at(1), 1, 1);
    pLeftGridLayout->addWidget(m_pLineEditList.at(2), 2, 1);
    pLeftGridLayout->addWidget(m_pLineEditList.at(3), 3, 1);
    pLeftGridLayout->addWidget(m_pBtnList.at(0),      4, 1);

    pLeftGridLayout->addWidget(m_pLineEditList.at(4), 0, 2);
    pLeftGridLayout->addWidget(m_pLineEditList.at(5), 1, 2);
    pLeftGridLayout->addWidget(m_pLineEditList.at(6), 2, 2);
    pLeftGridLayout->addWidget(m_pLineEditList.at(7), 3, 2);
    pLeftGridLayout->addWidget(m_pBtnList.at(1),      4, 2);


    pLeftGridLayout->addWidget(m_pLineEditList.at(8), 0, 3);
    pLeftGridLayout->addWidget(m_pLineEditList.at(9), 1, 3);
    pLeftGridLayout->addWidget(m_pLineEditList.at(10), 2, 3);
    pLeftGridLayout->addWidget(m_pLineEditList.at(11), 3, 3);
    pLeftGridLayout->addWidget(m_pBtnList.at(2),       4, 3);


    pLeftGridLayout->addWidget(m_pLineEditList.at(12), 0, 4);
    pLeftGridLayout->addWidget(m_pLineEditList.at(13), 1, 4);
    pLeftGridLayout->addWidget(m_pLineEditList.at(14), 2, 4);
    pLeftGridLayout->addWidget(m_pLineEditList.at(15), 3, 4);
    pLeftGridLayout->addWidget(m_pBtnList.at(3),       4, 4);


    pLeftGridLayout->addWidget(m_pErrCurvRemarkList.at(0), 5, 1);
    pLeftGridLayout->addWidget(m_pErrCurvRemarkList.at(1), 5, 2);
    pLeftGridLayout->addWidget(m_pErrCurvRemarkList.at(2), 5, 3);
    pLeftGridLayout->addWidget(m_pErrCurvRemarkList.at(3), 5, 4);





    QGridLayout *pRightGridLayout = new QGridLayout;
    pRightGridLayout->setMargin(0);
    pRightGridLayout->setVerticalSpacing(10);
    pRightGridLayout->setHorizontalSpacing(20);
    pRightGridLayout->addWidget(m_pHoleComboBox, 0, 0);
    pRightGridLayout->addWidget(m_pColorComboBox, 0, 1);
    pRightGridLayout->addWidget(m_pCalcBtn, 0, 2);
    //pRightGridLayout->addWidget(m_pFitModelComboBox, 1, 2);
    //pRightGridLayout->addWidget(m_pEnlargeNum, 1, 2);
    //pRightGridLayout->addWidget( m_pFitComboBox, 1, 2);

    pRightGridLayout->addWidget(m_pDataModelComboBox, 1, 0);
    pRightGridLayout->addWidget(m_pTestCheckBox, 1, 1);    
    pRightGridLayout->addWidget(m_pParamConfig, 1, 2);
    pRightGridLayout->addWidget(m_pSetParamBtn, 2, 0);
    pRightGridLayout->addWidget(m_pStandardCheckBox, 2, 1);
    pRightGridLayout->addWidget(m_pResultLineEdit, 2, 2);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->addSpacing(10);
    pTopLayout->addLayout(pLeftGridLayout);
    pTopLayout->addSpacing(20);
    pTopLayout->addLayout(pRightGridLayout);
    pTopLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addSpacing(10);
    pMainLayout->setSpacing(10);
    pMainLayout->addLayout(pTopLayout);
    pMainLayout->addWidget(m_pCustomPlot);
    pMainLayout->addWidget(m_pSetXY, 0, Qt::AlignLeft);
    this->setLayout(pMainLayout);
}
